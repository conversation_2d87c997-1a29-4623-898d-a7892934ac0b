import { auth as betterAuth } from "@/lib/auth";
import db from "@/db";
import { organization, member, user, userProfiles } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";
import { hasMinimumRole } from "@/lib/auth-utils";

export interface UpdateOrganizationData {
  name?: string;
  logo?: string;
  metadata?: Record<string, any>;
}

export interface InviteMemberData {
  email: string;
  role: "owner" | "admin" | "member";
  resend?: boolean;
}

/**
 * OrganizationService handles organization management operations
 * Combines Better Auth organization plugin with custom business logic
 */
export class OrganizationService {

  // =========================
  // Core Organization Operations
  // =========================

  /**
   * Get organization details by ID
   */
  static async getOrganization(organizationId: string) {
    try {
      const orgData = await db
        .select()
        .from(organization)
        .where(eq(organization.id, organizationId))
        .limit(1);

      if (!orgData[0]) {
        throw new HTTPException(404, { message: "Organization not found" });
      }

      return orgData[0];
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error("Error getting organization:", error);
      throw new HTTPException(500, { message: "Failed to get organization" });
    }
  }

  /**
   * Update organization details
   */
  static async updateOrganization(organizationId: string, updateData: UpdateOrganizationData) {
    try {
      const updatedOrg = await db
        .update(organization)
        .set({
          name: updateData.name,
          logo: updateData.logo,
          metadata: updateData.metadata ? JSON.stringify(updateData.metadata) : undefined,
        })
        .where(eq(organization.id, organizationId))
        .returning();

      if (!updatedOrg[0]) {
        throw new HTTPException(404, { message: "Organization not found" });
      }

      return updatedOrg[0];
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error("Error updating organization:", error);
      throw new HTTPException(500, { message: "Failed to update organization" });
    }
  }

  /**
   * List organization members with user details
   */
  static async listMembers(organizationId: string) {
    try {
      const members = await db
        .select({
          id: member.id,
          role: member.role,
          createdAt: member.createdAt,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            image: user.image,
          },
          profile: {
            displayName: userProfiles.displayName,
            firstName: userProfiles.firstName,
            lastName: userProfiles.lastName,
          }
        })
        .from(member)
        .innerJoin(user, eq(member.userId, user.id))
        .leftJoin(userProfiles, eq(userProfiles.userId, user.id))
        .where(eq(member.organizationId, organizationId));

      return members;
    } catch (error) {
      console.error("Error listing members:", error);
      throw new HTTPException(500, { message: "Failed to list organization members" });
    }
  }

  /**
   * Invite a new member to the organization
   */
  static async inviteMember(organizationId: string, inviteData: InviteMemberData) {
    try {
      // Validate role using ROLE_HIERARCHY
      if (!hasMinimumRole(inviteData.role, "viewer")) {
        throw new HTTPException(400, { message: "Invalid role specified" });
      }

      const invitation = await betterAuth.api.createInvitation({
        body: {
          organizationId,
          email: inviteData.email,
          role: inviteData.role,
          resend: inviteData.resend || false,
        },
      });

      return invitation;
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error("Error inviting member:", error);
      throw new HTTPException(500, { message: "Failed to invite member" });
    }
  }

  /**
   * Remove a member from the organization
   */
  static async removeMember(organizationId: string, memberIdOrEmail: string) {
    try {
      await betterAuth.api.removeMember({
        body: {
          organizationId,
          memberIdOrEmail,
        },
      });

      return { success: true };
    } catch (error) {
      console.error("Error removing member:", error);
      throw new HTTPException(500, { message: "Failed to remove member" });
    }
  }

  /**
   * Update member role within the organization
   */
  static async updateMemberRole(organizationId: string, memberId: string, newRole: string) {
    try {
      // Validate role using ROLE_HIERARCHY
      if (!hasMinimumRole(newRole, "viewer")) {
        throw new HTTPException(400, { message: "Invalid role specified" });
      }

      // Prevent changing owner role (business rule)
      const currentMember = await db
        .select()
        .from(member)
        .where(and(eq(member.id, memberId), eq(member.organizationId, organizationId)))
        .limit(1);

      if (!currentMember[0]) {
        throw new HTTPException(404, { message: "Member not found" });
      }

      if (currentMember[0].role === "owner") {
        throw new HTTPException(403, { message: "Cannot change owner role" });
      }

      const updatedMember = await db
        .update(member)
        .set({ role: newRole })
        .where(and(eq(member.id, memberId), eq(member.organizationId, organizationId)))
        .returning();

      return updatedMember[0];
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error("Error updating member role:", error);
      throw new HTTPException(500, { message: "Failed to update member role" });
    }
  }

  /**
   * Accept an organization invitation
   */
  static async acceptInvitation(invitationId: string) {
    try {
      const result = await betterAuth.api.acceptInvitation({
        body: { invitationId },
      });

      return result;
    } catch (error) {
      console.error("Error accepting invitation:", error);
      throw new HTTPException(500, { message: "Failed to accept invitation" });
    }
  }

  // =========================
  // Better Auth Wrapper Methods (Legacy)
  // =========================
  static async betterAuthAdminBanUser(args: { userId: string; banReason?: string; banExpiresIn?: number; headers?: HeadersInit }) {
    return betterAuth.api.banUser({ body: { userId: args.userId, banReason: args.banReason, banExpiresIn: args.banExpiresIn }, headers: args.headers });
  }

  static async betterAuthAdminUnbanUser(args: { userId: string; headers?: HeadersInit }) {
    return betterAuth.api.unbanUser({ body: { userId: args.userId }, headers: args.headers });
  }

  static async betterAuthAdminListUserSessions(args: { userId: string; headers?: HeadersInit }) {
    return betterAuth.api.listUserSessions({ body: { userId: args.userId }, headers: args.headers });
  }

  static async betterAuthAdminRevokeUserSession(args: { sessionToken: string; headers?: HeadersInit }) {
    return betterAuth.api.revokeUserSession({ body: { sessionToken: args.sessionToken }, headers: args.headers });
  }

  static async betterAuthOrgCreate(args: { name: string; slug: string; logo?: string; metadata?: Record<string, any>; keepCurrentActiveOrganization?: boolean; headers?: HeadersInit }) {
    return betterAuth.api.createOrganization({ body: { name: args.name, slug: args.slug, logo: args.logo, metadata: args.metadata, keepCurrentActiveOrganization: args.keepCurrentActiveOrganization }, headers: args.headers });
  }

  static async betterAuthOrgInvite(args: { organizationId?: string; email: string; role: string | string[]; resend?: boolean; headers?: HeadersInit }) {
    return betterAuth.api.createInvitation({
      body: { organizationId: args.organizationId, email: args.email, role: args.role as any, resend: args.resend },
      headers: args.headers,
    });
  }

  static async betterAuthOrgAcceptInvite(args: { invitationId: string; headers?: HeadersInit }) {
    return betterAuth.api.acceptInvitation({ body: { invitationId: args.invitationId }, headers: args.headers });
  }

  static async betterAuthOrgRemoveMember(args: { organizationId: string; memberIdOrEmail: string; headers?: HeadersInit }) {
    return betterAuth.api.removeMember({ body: { organizationId: args.organizationId, memberIdOrEmail: args.memberIdOrEmail }, headers: args.headers });
  }

  static async betterAuthOrgListMembers(args?: { organizationId?: string; limit?: number | string; offset?: number | string; sortBy?: string; sortDirection?: "asc" | "desc"; filterField?: string; filterValue?: string | number | boolean; filterOperator?: "lt" | "eq" | "ne" | "lte" | "gt" | "gte" | "contains"; headers?: HeadersInit }) {
    return betterAuth.api.listMembers({
      method: "GET",
      query: args ? {
        organizationId: args.organizationId,
        limit: args.limit,
        offset: args.offset,
        sortBy: args.sortBy,
        sortDirection: args.sortDirection,
        filterField: args.filterField,
        filterValue: args.filterValue as any,
        filterOperator: args.filterOperator as any,
      } : undefined,
      headers: args?.headers,
    });
  }
}

export default OrganizationService;


