import { auth as betterAuth } from "@/lib/auth";

/**
 * AuthService wraps Better Auth's API with a stable surface for our app.
 * All methods are prefixed with `betterAuth` to make callsites explicit.
 */
export class OrganizationService {

  // =========================
  // Admin
  // =========================
  static async betterAuthAdminBanUser(args: { userId: string; banReason?: string; banExpiresIn?: number; headers?: HeadersInit }) {
    return betterAuth.api.banUser({ body: { userId: args.userId, banReason: args.banReason, banExpiresIn: args.banExpiresIn }, headers: args.headers });
  }

  static async betterAuthAdminUnbanUser(args: { userId: string; headers?: HeadersInit }) {
    return betterAuth.api.unbanUser({ body: { userId: args.userId }, headers: args.headers });
  }

  static async betterAuthAdminListUserSessions(args: { userId: string; headers?: HeadersInit }) {
    return betterAuth.api.listUserSessions({ body: { userId: args.userId }, headers: args.headers });
  }

  static async betterAuthAdminRevokeUserSession(args: { sessionToken: string; headers?: HeadersInit }) {
    return betterAuth.api.revokeUserSession({ body: { sessionToken: args.sessionToken }, headers: args.headers });
  }

  // =========================
  // Organization
  // =========================
  static async betterAuthOrgCreate(args: { name: string; slug: string; logo?: string; metadata?: Record<string, any>; keepCurrentActiveOrganization?: boolean; headers?: HeadersInit }) {
    return betterAuth.api.createOrganization({ body: { name: args.name, slug: args.slug, logo: args.logo, metadata: args.metadata, keepCurrentActiveOrganization: args.keepCurrentActiveOrganization }, headers: args.headers });
  }

  static async betterAuthOrgInvite(args: { organizationId?: string; email: string; role: string | string[]; resend?: boolean; headers?: HeadersInit }) {
    return betterAuth.api.createInvitation({
      body: { organizationId: args.organizationId, email: args.email, role: args.role as any, resend: args.resend },
      headers: args.headers,
    });
  }

  static async betterAuthOrgAcceptInvite(args: { invitationId: string; headers?: HeadersInit }) {
    return betterAuth.api.acceptInvitation({ body: { invitationId: args.invitationId }, headers: args.headers });
  }

  static async betterAuthOrgRemoveMember(args: { organizationId: string; memberIdOrEmail: string; headers?: HeadersInit }) {
    return betterAuth.api.removeMember({ body: { organizationId: args.organizationId, memberIdOrEmail: args.memberIdOrEmail }, headers: args.headers });
  }

  static async betterAuthOrgListMembers(args?: { organizationId?: string; limit?: number | string; offset?: number | string; sortBy?: string; sortDirection?: "asc" | "desc"; filterField?: string; filterValue?: string | number | boolean; filterOperator?: "lt" | "eq" | "ne" | "lte" | "gt" | "gte" | "contains"; headers?: HeadersInit }) {
    return betterAuth.api.listMembers({
      method: "GET",
      query: args ? {
        organizationId: args.organizationId,
        limit: args.limit,
        offset: args.offset,
        sortBy: args.sortBy,
        sortDirection: args.sortDirection,
        filterField: args.filterField,
        filterValue: args.filterValue as any,
        filterOperator: args.filterOperator as any,
      } : undefined,
      headers: args?.headers,
    });
  }
}

export default OrganizationService;


