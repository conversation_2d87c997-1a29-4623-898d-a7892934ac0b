import type { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { getAuthenticatedUser, getUserWorkspace, requireMinimumRole, getValidatedBody, getValidatedParams } from "@/lib/auth-utils";
import { OrganizationService } from "./org.service";
import { createErrorResponse } from "@/middlewares/validation";

/**
 * Get current organization details
 * Returns the organization information for the authenticated user's workspace
 */
export async function getOrganization(c: Context) {
  try {
    const user = getAuthenticatedUser(c);
    const workspace = getUserWorkspace(c);

    const organization = await OrganizationService.getOrganization(workspace.id);

    return c.json(organization, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Get organization controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to get organization details",
          c.req.path
        )
      )
    });
  }
}

/**
 * Update organization details
 * Requires owner or admin role within the organization
 */
export async function updateOrganization(c: Context) {
  try {
    const workspace = getUserWorkspace(c);
    
    // Require minimum admin role to update organization
    requireMinimumRole(c, "admin");

    const body = getValidatedBody(c);
    
    const updatedOrganization = await OrganizationService.updateOrganization(workspace.id, body);

    return c.json(updatedOrganization, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Update organization controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to update organization",
          c.req.path
        )
      )
    });
  }
}

/**
 * List organization members
 * Requires member role or higher within the organization
 */
export async function listMembers(c: Context) {
  try {
    const workspace = getUserWorkspace(c);

    const members = await OrganizationService.listMembers(workspace.id);

    return c.json(members, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("List members controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to list organization members",
          c.req.path
        )
      )
    });
  }
}

/**
 * Invite a new member to the organization
 * Requires admin role or higher within the organization
 */
export async function inviteMember(c: Context) {
  try {
    const workspace = getUserWorkspace(c);
    
    // Require minimum admin role to invite members
    requireMinimumRole(c, "admin");

    const body = getValidatedBody(c);
    
    const invitation = await OrganizationService.inviteMember(workspace.id, body);

    return c.json(invitation, 201);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Invite member controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to invite member",
          c.req.path
        )
      )
    });
  }
}

/**
 * Remove a member from the organization
 * Requires admin role or higher within the organization
 */
export async function removeMember(c: Context) {
  try {
    const user = getAuthenticatedUser(c);
    const workspace = getUserWorkspace(c);
    
    // Require minimum admin role to remove members
    requireMinimumRole(c, "admin");

    const params = getValidatedParams(c);
    
    await OrganizationService.removeMember(workspace.id, params.memberIdOrEmail);

    return c.json({ success: true }, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Remove member controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to remove member",
          c.req.path
        )
      )
    });
  }
}

/**
 * Update member role within the organization
 * Requires owner role within the organization
 */
export async function updateMemberRole(c: Context) {
  try {
    const user = getAuthenticatedUser(c);
    const workspace = getUserWorkspace(c);
    
    // Require owner role to update member roles
    requireMinimumRole(c, "owner");

    const params = getValidatedParams(c);
    const body = getValidatedBody(c);
    
    const updatedMember = await OrganizationService.updateMemberRole(
      workspace.id, 
      params.memberId, 
      body.role
    );

    return c.json(updatedMember, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Update member role controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to update member role",
          c.req.path
        )
      )
    });
  }
}

/**
 * Accept an organization invitation
 * Public endpoint for users with valid invitation tokens
 */
export async function acceptInvitation(c: Context) {
  try {
    const body = getValidatedBody(c);
    
    const result = await OrganizationService.acceptInvitation(body.invitationId);

    return c.json(result, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Accept invitation controller error:", error);
    throw new HTTPException(500, { 
      message: JSON.stringify(
        createErrorResponse(
          "INTERNAL_SERVER_ERROR",
          "Failed to accept invitation",
          c.req.path
        )
      )
    });
  }
}
