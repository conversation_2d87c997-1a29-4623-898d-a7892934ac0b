import { createRoute } from "@hono/zod-openapi";
import { z } from "@hono/zod-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { jsonContent, jsonContentRequired } from "stoker/openapi/helpers";
import { createErrorSchema } from "stoker/openapi/schemas";
import { validateBody, validateParams } from "@/middlewares/validation";

// =============================================================================
// SCHEMAS
// =============================================================================

// Organization schemas
const OrganizationSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string().nullable(),
  logo: z.string().nullable(),
  companyType: z.string().nullable(),
  createdAt: z.string(),
  metadata: z.string().nullable(),
}).openapi("Organization");

const UpdateOrganizationRequestSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  logo: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
}).openapi("UpdateOrganizationRequest");

// Member schemas
const MemberSchema = z.object({
  id: z.string(),
  role: z.enum(["owner", "admin", "member"]),
  createdAt: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    image: z.string().nullable(),
  }),
  profile: z.object({
    displayName: z.string().nullable(),
    firstName: z.string().nullable(),
    lastName: z.string().nullable(),
  }).nullable(),
}).openapi("Member");

const InviteMemberRequestSchema = z.object({
  email: z.string().email(),
  role: z.enum(["admin", "member"]),
  resend: z.boolean().optional(),
}).openapi("InviteMemberRequest");

const UpdateMemberRoleRequestSchema = z.object({
  role: z.enum(["admin", "member"]),
}).openapi("UpdateMemberRoleRequest");

const AcceptInvitationRequestSchema = z.object({
  invitationId: z.string(),
}).openapi("AcceptInvitationRequest");

// Response schemas
const OrganizationResponseSchema = OrganizationSchema.openapi("OrganizationResponse");
const MembersListResponseSchema = z.array(MemberSchema).openapi("MembersListResponse");
const SuccessResponseSchema = z.object({
  success: z.boolean(),
}).openapi("SuccessResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

export const getOrganizationRoute = createRoute({
  method: "get",
  path: "/v1/organizations/current",
  summary: "Get current organization",
  description: "Get details of the current user's organization/workspace",
  tags: ["Organizations"],
  responses: {
    [HttpStatusCodes.OK]: jsonContent(
      OrganizationResponseSchema,
      "Organization details retrieved successfully"
    ),
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      createErrorSchema("AUTHENTICATION_REQUIRED"),
      "Authentication required"
    ),
    [HttpStatusCodes.NOT_FOUND]: jsonContent(
      createErrorSchema("ORGANIZATION_NOT_FOUND"),
      "Organization not found"
    ),
  },
});

export const updateOrganizationRoute = createRoute({
  method: "put",
  path: "/v1/organizations/current",
  summary: "Update current organization",
  description: "Update details of the current user's organization/workspace. Requires admin role or higher.",
  tags: ["Organizations"],
  request: {
    body: jsonContentRequired(
      UpdateOrganizationRequestSchema,
      "Organization update data"
    ),
  },
  middleware: [validateBody(UpdateOrganizationRequestSchema)],
  responses: {
    [HttpStatusCodes.OK]: jsonContent(
      OrganizationResponseSchema,
      "Organization updated successfully"
    ),
    [HttpStatusCodes.BAD_REQUEST]: jsonContent(
      createErrorSchema("VALIDATION_ERROR"),
      "Invalid request data"
    ),
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      createErrorSchema("AUTHENTICATION_REQUIRED"),
      "Authentication required"
    ),
    [HttpStatusCodes.FORBIDDEN]: jsonContent(
      createErrorSchema("INSUFFICIENT_PERMISSIONS"),
      "Insufficient permissions"
    ),
  },
});

export const listMembersRoute = createRoute({
  method: "get",
  path: "/v1/organizations/current/members",
  summary: "List organization members",
  description: "Get a list of all members in the current organization",
  tags: ["Organizations"],
  responses: {
    [HttpStatusCodes.OK]: jsonContent(
      MembersListResponseSchema,
      "Members list retrieved successfully"
    ),
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      createErrorSchema("AUTHENTICATION_REQUIRED"),
      "Authentication required"
    ),
  },
});

export const inviteMemberRoute = createRoute({
  method: "post",
  path: "/v1/organizations/current/members",
  summary: "Invite new member",
  description: "Invite a new member to the organization. Requires admin role or higher.",
  tags: ["Organizations"],
  request: {
    body: jsonContentRequired(
      InviteMemberRequestSchema,
      "Member invitation data"
    ),
  },
  middleware: [validateBody(InviteMemberRequestSchema)],
  responses: {
    [HttpStatusCodes.CREATED]: jsonContent(
      z.object({ success: z.boolean(), invitationId: z.string() }),
      "Member invited successfully"
    ),
    [HttpStatusCodes.BAD_REQUEST]: jsonContent(
      createErrorSchema("VALIDATION_ERROR"),
      "Invalid request data"
    ),
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      createErrorSchema("AUTHENTICATION_REQUIRED"),
      "Authentication required"
    ),
    [HttpStatusCodes.FORBIDDEN]: jsonContent(
      createErrorSchema("INSUFFICIENT_PERMISSIONS"),
      "Insufficient permissions"
    ),
  },
});

export const removeMemberRoute = createRoute({
  method: "delete",
  path: "/v1/organizations/current/members/{memberIdOrEmail}",
  summary: "Remove member",
  description: "Remove a member from the organization. Requires admin role or higher.",
  tags: ["Organizations"],
  request: {
    params: z.object({
      memberIdOrEmail: z.string(),
    }),
  },
  middleware: [validateParams(z.object({ memberIdOrEmail: z.string() }))],
  responses: {
    [HttpStatusCodes.OK]: jsonContent(
      SuccessResponseSchema,
      "Member removed successfully"
    ),
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      createErrorSchema("AUTHENTICATION_REQUIRED"),
      "Authentication required"
    ),
    [HttpStatusCodes.FORBIDDEN]: jsonContent(
      createErrorSchema("INSUFFICIENT_PERMISSIONS"),
      "Insufficient permissions"
    ),
    [HttpStatusCodes.NOT_FOUND]: jsonContent(
      createErrorSchema("MEMBER_NOT_FOUND"),
      "Member not found"
    ),
  },
});

export const updateMemberRoleRoute = createRoute({
  method: "put",
  path: "/v1/organizations/current/members/{memberId}/role",
  summary: "Update member role",
  description: "Update a member's role within the organization. Requires owner role.",
  tags: ["Organizations"],
  request: {
    params: z.object({
      memberId: z.string(),
    }),
    body: jsonContentRequired(
      UpdateMemberRoleRequestSchema,
      "Member role update data"
    ),
  },
  middleware: [
    validateParams(z.object({ memberId: z.string() })),
    validateBody(UpdateMemberRoleRequestSchema)
  ],
  responses: {
    [HttpStatusCodes.OK]: jsonContent(
      MemberSchema,
      "Member role updated successfully"
    ),
    [HttpStatusCodes.BAD_REQUEST]: jsonContent(
      createErrorSchema("VALIDATION_ERROR"),
      "Invalid request data"
    ),
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      createErrorSchema("AUTHENTICATION_REQUIRED"),
      "Authentication required"
    ),
    [HttpStatusCodes.FORBIDDEN]: jsonContent(
      createErrorSchema("INSUFFICIENT_PERMISSIONS"),
      "Insufficient permissions"
    ),
    [HttpStatusCodes.NOT_FOUND]: jsonContent(
      createErrorSchema("MEMBER_NOT_FOUND"),
      "Member not found"
    ),
  },
});

export const acceptInvitationRoute = createRoute({
  method: "post",
  path: "/v1/organizations/invitations/accept",
  summary: "Accept organization invitation",
  description: "Accept an invitation to join an organization",
  tags: ["Organizations"],
  request: {
    body: jsonContentRequired(
      AcceptInvitationRequestSchema,
      "Invitation acceptance data"
    ),
  },
  middleware: [validateBody(AcceptInvitationRequestSchema)],
  responses: {
    [HttpStatusCodes.OK]: jsonContent(
      SuccessResponseSchema,
      "Invitation accepted successfully"
    ),
    [HttpStatusCodes.BAD_REQUEST]: jsonContent(
      createErrorSchema("VALIDATION_ERROR"),
      "Invalid request data"
    ),
    [HttpStatusCodes.NOT_FOUND]: jsonContent(
      createErrorSchema("INVITATION_NOT_FOUND"),
      "Invitation not found or expired"
    ),
  },
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./org.controller";

export const organizationRouter = createRouter()
  .openapi(getOrganizationRoute, handlers.getOrganization)
  .openapi(updateOrganizationRoute, handlers.updateOrganization)
  .openapi(listMembersRoute, handlers.listMembers)
  .openapi(inviteMemberRoute, handlers.inviteMember)
  .openapi(removeMemberRoute, handlers.removeMember)
  .openapi(updateMemberRoleRoute, handlers.updateMemberRole)
  .openapi(acceptInvitationRoute, handlers.acceptInvitation);

export default organizationRouter;
