import { HTTPException } from "hono/http-exception";
import { eq, and, sql, desc, asc, like, or, count } from "drizzle-orm";
import db from "@/db";
import { listings, listingDetails, listingStatusHistory, listingNotes, userProfiles } from "@/db/schema";
import { CsvParser, type ParsedCsvData } from "@/lib/csv-parser";
import { createListingRequestSchema, saveDraftListingRequestSchema } from "./listings.routes";
import type { z } from "zod";

// Use the generated schema types instead of manual interfaces
export type CreateListingData = z.infer<typeof createListingRequestSchema> & {
  organizationId: string;
  createdBy: string;
};

export type SaveDraftListingData = z.infer<typeof saveDraftListingRequestSchema> & {
  organizationId: string;
  createdBy: string;
};

export type UpdateListingData = Partial<CreateListingData>;

export interface ListingDetailsData {
  business_description?: string;
  brief_description?: string;
  financial_details?: {
    revenue_2023?: number;
    ebitda?: number;
    assets_included?: string[];
    inventory_value?: number;
    additional_financial_info?: any;
  };
  operations?: {
    business_model?: string;
    key_features?: string[];
    competitive_advantages?: string[];
    operational_details?: any;
  };
  growth_opportunities?: string[];
  reason_for_sale?: string;
  training_period?: string;
  support_type?: string;
  financing_available?: boolean;
  equipment_highlights?: string[];
  supplier_relationships?: string;
  real_estate_status?: string;
  lease_details?: {
    lease_terms?: string;
    monthly_rent?: number;
    lease_expiration?: string;
    renewal_options?: string;
    landlord_info?: any;
  };
}

export interface ListingFilters {
  page?: number;
  limit?: number;
  status?: string;
  industry?: string;
  assignedTo?: string;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface StatusChangeData {
  status: string;
  reason?: string;
  notes?: string;
  changedBy: string;
  organizationId: string;
}

export interface CreateListingNoteData {
  listingId: string;
  organizationId: string;
  createdBy: string;
  content: string;
  mentions?: string[];
  isPrivate?: boolean;
}

export interface UpdateListingNoteData {
  content?: string;
  mentions?: string[];
  isPrivate?: boolean;
}

export class ListingsService {
  
  // Valid status values that match the database constraint
  private static readonly VALID_STATUS_VALUES = [
    'draft',
    'active', 
    'pending',
    'sold',
    'withdrawn'
  ] as const;

  /**
   * Validate status value against allowed values
   */
  private static validateStatus(status?: string | null): string {
    if (!status) return 'draft'; // default status

    const normalizedStatus = status.toLowerCase().trim();

    // Handle common variations to match actual DB constraint
    const statusMap: Record<string, string> = {
      'under contract': 'pending',
      'undercontract': 'pending',
      'under_contract': 'pending',
      'pending': 'pending',
      'closed': 'sold',
      'available': 'active',
      'listed': 'active',
      'off market': 'withdrawn',
      'off-market': 'withdrawn',
      'offmarket': 'withdrawn',
      'confidential': 'active', // Map confidential to active since DB doesn't have it
      'expired': 'withdrawn',  // Map expired to withdrawn
    };

    const mappedStatus = statusMap[normalizedStatus] || normalizedStatus;

    if (this.VALID_STATUS_VALUES.includes(mappedStatus as any)) {
      return mappedStatus;
    }

    // If invalid status, default to draft
    console.warn(`Invalid status value "${status}" provided, defaulting to "draft"`);
    return 'draft';
  }

  /**
   * Validate draft data for transition to non-draft status
   * Returns validation errors if any required fields are missing
   */
  private static validateDraftDataForTransition(draftData: any): string[] {
    const errors: string[] = [];

    // Handle corrupted or invalid draft data
    if (!draftData || typeof draftData !== 'object') {
      errors.push('Draft data is corrupted or invalid');
      return errors;
    }

    // Check required fields for non-draft listings
    if (!draftData.businessName || typeof draftData.businessName !== 'string' || draftData.businessName.trim() === '') {
      errors.push('Business name is required');
    }

    if (!draftData.industry || typeof draftData.industry !== 'string' || draftData.industry.trim() === '') {
      errors.push('Industry is required');
    }

    // Additional business logic validations with type checking
    if (draftData.askingPrice !== undefined) {
      const price = Number(draftData.askingPrice);
      if (isNaN(price) || price <= 0) {
        errors.push('Asking price must be a valid number greater than 0');
      }
    }

    if (draftData.yearEstablished !== undefined) {
      const year = Number(draftData.yearEstablished);
      const currentYear = new Date().getFullYear();
      if (isNaN(year) || year < 1800 || year > currentYear) {
        errors.push(`Year established must be a valid year between 1800 and ${currentYear}`);
      }
    }

    if (draftData.employees !== undefined) {
      const employees = Number(draftData.employees);
      if (isNaN(employees) || employees < 0) {
        errors.push('Number of employees must be a valid number and cannot be negative');
      }
    }

    if (draftData.ownerHoursWeek !== undefined) {
      const hours = Number(draftData.ownerHoursWeek);
      if (isNaN(hours) || hours < 0 || hours > 168) {
        errors.push('Owner hours per week must be a valid number between 0 and 168');
      }
    }

    if (draftData.dateListed !== undefined) {
      const date = new Date(draftData.dateListed);
      if (isNaN(date.getTime())) {
        errors.push('Date listed must be a valid date');
      }
    }

    return errors;
  }

  /**
   * Safely extract and validate draft data from listing
   */
  private static extractDraftData(listing: any): any | null {
    try {
      if (!listing._draft) {
        return null;
      }

      // If _draft is already an object, return it
      if (typeof listing._draft === 'object') {
        return listing._draft;
      }

      // If _draft is a string, try to parse it as JSON
      if (typeof listing._draft === 'string') {
        return JSON.parse(listing._draft);
      }

      return null;
    } catch (error) {
      console.error('Error parsing draft data:', error);
      return null;
    }
  }

  /**
   * Calculate days listed for a listing based on date_listed
   */
  private static calculateDaysListed(dateListed?: string | null): number | null {
    if (!dateListed) return null;

    const listedDate = new Date(dateListed);
    const today = new Date();
    const diffTime = today.getTime() - listedDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays >= 0 ? diffDays : 0;
  }

  /**
   * Auto-update days_listed field for all active listings
   */
  static async updateDaysListedBatch() {
    const activeListings = await db
      .select({ id: listings.id, dateListed: listings.dateListed })
      .from(listings)
      .where(
        and(
          eq(listings.status, 'active'),
          sql`${listings.dateListed} IS NOT NULL`
        )
      );

    for (const listing of activeListings) {
      const daysListed = this.calculateDaysListed(listing.dateListed);
      if (daysListed !== null) {
        await db
          .update(listings)
          .set({
            daysListed,
            updatedAt: sql`now()`
          })
          .where(eq(listings.id, listing.id));
      }
    }
  }

  /**
   * Get all listings with filtering, sorting and pagination
   */
  static async getListings(filters: ListingFilters, organizationId: string) {
    const {
      page = 1,
      limit = 20,
      status,
      industry,
      assignedTo,
      minPrice,
      maxPrice,
      location,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search
    } = filters;

    // Build where conditions
    const conditions = [eq(listings.organizationId, organizationId)];

    if (status) {
      conditions.push(eq(listings.status, status));
    }

    if (industry) {
      conditions.push(eq(listings.industry, industry));
    }

    if (assignedTo) {
      conditions.push(eq(listings.assignedTo, assignedTo));
    }

    if (minPrice !== undefined && maxPrice !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric BETWEEN ${minPrice} AND ${maxPrice}`);
    } else if (minPrice !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric >= ${minPrice}`);
    } else if (maxPrice !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric <= ${maxPrice}`);
    }

    if (location) {
      conditions.push(like(listings.generalLocation, `%${location}%`));
    }

    if (search) {
      conditions.push(
        or(
          like(listings.businessName, `%${search}%`),
          like(listings.industry, `%${search}%`),
          like(listings.description, `%${search}%`),
          like(listings.generalLocation, `%${search}%`)
        )!
      );
    }

    // Apply sorting
    const sortColumn = sortBy === 'asking_price' ? sql`${listings.askingPrice}::numeric` :
      sortBy === 'business_name' ? listings.businessName :
        sortBy === 'date_listed' ? listings.dateListed :
          sortBy === 'days_listed' ? listings.daysListed :
            sortBy === 'updated_at' ? listings.updatedAt :
              listings.createdAt;

    const orderByClause = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Get total count for pagination
    const [{ total }] = await db
      .select({ total: count() })
      .from(listings)
      .where(and(...conditions));

    // Apply pagination and get results
    const offset = (page - 1) * limit;
    const results = await db
      .select({
        id: listings.id,
        organizationId: listings.organizationId,
        createdBy: listings.createdBy,
        assignedTo: listings.assignedTo,
        businessName: listings.businessName,
        industry: listings.industry,
        askingPrice: listings.askingPrice,
        cashFlowSde: listings.cashFlowSde,
        annualRevenue: listings.annualRevenue,
        status: listings.status,
        generalLocation: listings.generalLocation,
        yearEstablished: listings.yearEstablished,
        employees: listings.employees,
        ownerHoursWeek: listings.ownerHoursWeek,
        dateListed: listings.dateListed,
        daysListed: listings.daysListed,
        title: listings.title,
        description: listings.description,
        price: listings.price,
        teamVisibility: listings.teamVisibility,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
      })
      .from(listings)
      .where(and(...conditions))
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get a single listing by ID with optional details
   * If status is 'draft', return data from _draft column merged with existing data
   */
  static async getListingById(id: string, organizationId: string, includeDetails = true) {
    const listing = await db
      .select()
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
      .limit(1);

    if (listing.length === 0) {
      throw new HTTPException(404, { message: "Listing not found" });
    }

    let details = null;
    if (includeDetails) {
      const detailsResult = await db
        .select()
        .from(listingDetails)
        .where(eq(listingDetails.listingId, id))
        .limit(1);

      details = detailsResult[0] || null;
    }

    const listingData = listing[0];

    // If status is draft and _draft data exists, merge draft data with existing data
    if (listingData.status === 'draft' && listingData._draft) {
      const draftData = listingData._draft as any;
      
      // Merge draft data with existing listing data, giving priority to draft data
      const mergedListing = {
        ...listingData,
        ...draftData,
        // Keep system fields from the original listing
        id: listingData.id,
        organizationId: listingData.organizationId,
        createdBy: listingData.createdBy,
        status: listingData.status,
        createdAt: listingData.createdAt,
        updatedAt: listingData.updatedAt,
      };

      // If draft contains details, merge with existing details
      if (draftData.details && details) {
        details = {
          ...details,
          ...draftData.details,
          // Keep system fields from the original details
          id: details.id,
          listingId: details.listingId,
          createdAt: details.createdAt,
          updatedAt: details.updatedAt,
        };
      } else if (draftData.details) {
        details = draftData.details;
      }

      return {
        ...mergedListing,
        details,
      };
    }

    return {
      ...listingData,
      details,
    };
  }

  /**
   * Save listing as draft - stores data in _draft column
   */
  static async saveDraftListing(data: SaveDraftListingData): Promise<any> {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Create the main listing record with minimal required fields
      const [listing] = await tx
        .insert(listings)
        .values({
          organizationId: data.organizationId,
          createdBy: data.createdBy,
          // Use placeholder values for required fields if not provided
          businessName: data.businessName || 'Draft Listing',
          industry: data.industry || 'Not Specified',
          status: 'draft',
          teamVisibility: data.teamVisibility || 'all',
          listingType: data.listingType || 'business_sale',
          // Store all provided data in _draft column
          _draft: data,
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      // Log initial status
      await tx
        .insert(listingStatusHistory)
        .values({
          listingId: listing.id,
          organizationId: data.organizationId,
          changedBy: data.createdBy,
          fromStatus: null,
          toStatus: 'draft',
          reason: 'Draft listing created',
          createdAt: now,
        });

      return {
        ...listing,
        // Return the draft data as the main data for the response
        ...data,
        id: listing.id,
        status: 'draft',
        createdAt: listing.createdAt,
        updatedAt: listing.updatedAt,
      };
    });
  }

  /**
   * Update existing draft listing - updates data in _draft column
   */
  static async updateDraftListing(id: string, data: SaveDraftListingData, organizationId: string): Promise<any> {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Get existing listing and verify it's a draft
      const [existingListing] = await tx
        .select()
        .from(listings)
        .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
        .limit(1);

      if (!existingListing) {
        throw new HTTPException(404, { message: "Listing not found" });
      }

      if (existingListing.status !== 'draft') {
        throw new HTTPException(400, {
          message: "Cannot update non-draft listing as draft. Use the regular update endpoint instead."
        });
      }

      // Merge new data with existing draft data
      const existingDraftData = (existingListing._draft as any) || {};
      const mergedDraftData = {
        ...existingDraftData,
        ...data,
        // Preserve system fields
        organizationId: data.organizationId,
        createdBy: existingListing.createdBy, // Don't allow changing the creator
      };

      // Update the listing with merged draft data
      const [updatedListing] = await tx
        .update(listings)
        .set({
          // Update placeholder values if provided in draft data
          businessName: data.businessName || existingListing.businessName,
          industry: data.industry || existingListing.industry,
          teamVisibility: data.teamVisibility || existingListing.teamVisibility,
          listingType: data.listingType || existingListing.listingType,
          // Store merged data in _draft column
          _draft: mergedDraftData,
          updatedAt: now,
        })
        .where(eq(listings.id, id))
        .returning();

      return {
        ...updatedListing,
        // Return the merged draft data as the main data for the response
        ...mergedDraftData,
        id: updatedListing.id,
        status: 'draft',
        createdAt: updatedListing.createdAt,
        updatedAt: updatedListing.updatedAt,
      };
    });
  }

  /**
   * Create a new listing
   */
  static async createListing(data: CreateListingData): Promise<any> {
    const now = new Date().toISOString();

    // Calculate days_listed if dateListed is provided
    const daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : null;

    return await db.transaction(async (tx) => {
      // Create the main listing record
      const [listing] = await tx
        .insert(listings)
        .values({
          organizationId: data.organizationId,
          createdBy: data.createdBy,
          assignedTo: data.assignedTo || null,
          businessName: data.businessName,
          industry: data.industry,
          askingPrice: data.askingPrice?.toString(),
          cashFlowSde: data.cashFlowSde?.toString(),
          annualRevenue: data.annualRevenue?.toString(),
          status: this.validateStatus(data.status),
          generalLocation: data.generalLocation,
          yearEstablished: data.yearEstablished,
          employees: data.employees,
          ownerHoursWeek: data.ownerHoursWeek,
          dateListed: data.dateListed,
          daysListed: daysListed,
          // Legacy fields
          title: data.title || data.businessName,
          description: data.description,
          price: data.askingPrice?.toString(), // Copy to legacy field
          teamVisibility: data.teamVisibility || 'all',
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      // Create listing details if provided
      let details = null;
      if (data.details) {
        [details] = await tx
          .insert(listingDetails)
          .values({
            listingId: listing.id,
            businessDescription: data.details.businessDescription,
            briefDescription: data.details.briefDescription,
            financialDetails: data.details.financialDetails || {},
            operations: data.details.operations || {},
            growthOpportunities: data.details.growthOpportunities || [],
            reasonForSale: data.details.reasonForSale,
            trainingPeriod: data.details.trainingPeriod,
            supportType: data.details.supportType,
            financingAvailable: data.details.financingAvailable || false,
            equipmentHighlights: data.details.equipmentHighlights || [],
            supplierRelationships: data.details.supplierRelationships,
            realEstateStatus: data.details.realEstateStatus,
            leaseDetails: data.details.leaseDetails || {},
            createdAt: now,
            updatedAt: now,
          })
          .returning();
      }

      // Log initial status
      await tx
        .insert(listingStatusHistory)
        .values({
          listingId: listing.id,
          organizationId: data.organizationId,
          changedBy: data.createdBy,
          fromStatus: null,
          toStatus: data.status || 'draft',
          reason: 'Initial listing creation',
          createdAt: now,
        });

      return {
        ...listing,
        details,
      };
    });
  }

  /**
   * Update an existing listing
   */
  static async updateListing(id: string, data: UpdateListingData, organizationId: string): Promise<any> {
    const now = new Date().toISOString();

    // Check if listing exists
    const existingListing = await this.getListingById(id, organizationId, false);

    // Calculate days_listed if dateListed is being updated
    const daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : undefined;

    return await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: now,
      };

      // Update core fields
      if (data.businessName !== undefined) updateData.businessName = data.businessName;
      if (data.industry !== undefined) updateData.industry = data.industry;
      if (data.askingPrice !== undefined) {
        updateData.askingPrice = data.askingPrice?.toString();
        updateData.price = data.askingPrice?.toString(); // Update legacy field
      }
      if (data.cashFlowSde !== undefined) updateData.cashFlowSde = data.cashFlowSde?.toString();
      if (data.annualRevenue !== undefined) updateData.annualRevenue = data.annualRevenue?.toString();
      if (data.generalLocation !== undefined) updateData.generalLocation = data.generalLocation;
      if (data.yearEstablished !== undefined) updateData.yearEstablished = data.yearEstablished;
      if (data.employees !== undefined) updateData.employees = data.employees;
      if (data.ownerHoursWeek !== undefined) updateData.ownerHoursWeek = data.ownerHoursWeek;
      if (data.dateListed !== undefined) {
        updateData.dateListed = data.dateListed;
        updateData.daysListed = daysListed;
      }
      if (data.assignedTo !== undefined) updateData.assignedTo = data.assignedTo;
      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.teamVisibility !== undefined) updateData.teamVisibility = data.teamVisibility;

      // Update main listing
      const [updatedListing] = await tx
        .update(listings)
        .set(updateData)
        .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
        .returning();

      // Update or create details if provided
      let details = null;
      if (data.details) {
        const existingDetails = await tx
          .select()
          .from(listingDetails)
          .where(eq(listingDetails.listingId, id))
          .limit(1);

        if (existingDetails.length > 0) {
          // Update existing details
          [details] = await tx
            .update(listingDetails)
            .set({
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails,
              operations: data.details.operations,
              growthOpportunities: data.details.growthOpportunities,
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable,
              equipmentHighlights: data.details.equipmentHighlights,
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails,
              updatedAt: now,
            })
            .where(eq(listingDetails.listingId, id))
            .returning();
        } else {
          // Create new details
          [details] = await tx
            .insert(listingDetails)
            .values({
              listingId: id,
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails || {},
              operations: data.details.operations || {},
              growthOpportunities: data.details.growthOpportunities || [],
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable || false,
              equipmentHighlights: data.details.equipmentHighlights || [],
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails || {},
              createdAt: now,
              updatedAt: now,
            })
            .returning();
        }
      }

      return {
        ...updatedListing,
        details,
      };
    });
  }

  /**
   * Update an existing listing with status tracking
   * Combines listing update with status history tracking when status changes
   */
  static async updateListingWithStatus(id: string, data: UpdateListingData & { reason?: string; notes?: string }, organizationId: string, userId: string): Promise<any> {
    const now = new Date().toISOString();

    // Check if listing exists
    const existingListing = await this.getListingById(id, organizationId, false);

    // Calculate days_listed if dateListed is being updated
    const daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : undefined;

    return await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: now,
      };

      // Track if status is changing for history logging
      const isStatusChanging = data.status !== undefined && data.status !== existingListing.status;
      const fromStatus = existingListing.status;

      // Update core fields
      if (data.businessName !== undefined) updateData.businessName = data.businessName;
      if (data.industry !== undefined) updateData.industry = data.industry;
      if (data.askingPrice !== undefined) {
        updateData.askingPrice = data.askingPrice?.toString();
        updateData.price = data.askingPrice?.toString(); // Update legacy field
      }
      if (data.cashFlowSde !== undefined) updateData.cashFlowSde = data.cashFlowSde?.toString();
      if (data.annualRevenue !== undefined) updateData.annualRevenue = data.annualRevenue?.toString();
      if (data.generalLocation !== undefined) updateData.generalLocation = data.generalLocation;
      if (data.yearEstablished !== undefined) updateData.yearEstablished = data.yearEstablished;
      if (data.employees !== undefined) updateData.employees = data.employees;
      if (data.ownerHoursWeek !== undefined) updateData.ownerHoursWeek = data.ownerHoursWeek;
      if (data.dateListed !== undefined) {
        updateData.dateListed = data.dateListed;
        updateData.daysListed = daysListed;
      }
      if (data.assignedTo !== undefined) updateData.assignedTo = data.assignedTo;
      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.teamVisibility !== undefined) updateData.teamVisibility = data.teamVisibility;
      
      // Handle status update with validation
      if (data.status !== undefined) {
        updateData.status = this.validateStatus(data.status);

        // If transitioning from draft to non-draft, validate and remap draft data
        if (fromStatus === 'draft' && updateData.status !== 'draft') {
          const draftData = this.extractDraftData(existingListing);

          if (!draftData) {
            throw new HTTPException(400, {
              message: `Cannot transition from draft to ${updateData.status}. Draft data is missing or corrupted.`
            });
          }

          // Validate draft data has all required fields
          const validationErrors = this.validateDraftDataForTransition(draftData);
          if (validationErrors.length > 0) {
            throw new HTTPException(400, {
              message: `Cannot transition from draft to ${updateData.status}. Validation errors: ${validationErrors.join(', ')}`
            });
          }

            // Remap draft data to main listing fields
            // Only update fields that aren't already being updated in this request
            if (data.businessName === undefined && draftData.businessName) {
              updateData.businessName = draftData.businessName;
            }
            if (data.industry === undefined && draftData.industry) {
              updateData.industry = draftData.industry;
            }
            if (data.askingPrice === undefined && draftData.askingPrice) {
              updateData.askingPrice = draftData.askingPrice.toString();
              updateData.price = draftData.askingPrice.toString();
            }
            if (data.cashFlowSde === undefined && draftData.cashFlowSde) {
              updateData.cashFlowSde = draftData.cashFlowSde.toString();
            }
            if (data.annualRevenue === undefined && draftData.annualRevenue) {
              updateData.annualRevenue = draftData.annualRevenue.toString();
            }
            if (data.generalLocation === undefined && draftData.generalLocation) {
              updateData.generalLocation = draftData.generalLocation;
            }
            if (data.yearEstablished === undefined && draftData.yearEstablished) {
              updateData.yearEstablished = draftData.yearEstablished;
            }
            if (data.employees === undefined && draftData.employees) {
              updateData.employees = draftData.employees;
            }
            if (data.ownerHoursWeek === undefined && draftData.ownerHoursWeek) {
              updateData.ownerHoursWeek = draftData.ownerHoursWeek;
            }
            if (data.dateListed === undefined && draftData.dateListed) {
              updateData.dateListed = draftData.dateListed;
              updateData.daysListed = this.calculateDaysListed(draftData.dateListed);
            }
            if (data.assignedTo === undefined && draftData.assignedTo) {
              updateData.assignedTo = draftData.assignedTo;
            }
            if (data.title === undefined && draftData.title) {
              updateData.title = draftData.title;
            }
            if (data.description === undefined && draftData.description) {
              updateData.description = draftData.description;
            }
            if (data.teamVisibility === undefined && draftData.teamVisibility) {
              updateData.teamVisibility = draftData.teamVisibility;
            }

            // Store draft details for later processing
            if (draftData.details && !data.details) {
              data.details = draftData.details;
            }
          }

          // Clear the _draft column after successful remapping
          updateData._draft = null;
        }
      

      // Update main listing
      const [updatedListing] = await tx
        .update(listings)
        .set(updateData)
        .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
        .returning();

      // Log status change if status was modified
      let statusChange = null;
      if (isStatusChanging) {
        [statusChange] = await tx
          .insert(listingStatusHistory)
          .values({
            listingId: id,
            organizationId: organizationId,
            changedBy: userId,
            fromStatus: fromStatus,
            toStatus: updateData.status || data.status,
            reason: data.reason || 'Status updated via listing update',
            notes: data.notes,
            createdAt: now,
          })
          .returning();
      }

      // Update or create details if provided
      let details = null;
      if (data.details) {
        const existingDetails = await tx
          .select()
          .from(listingDetails)
          .where(eq(listingDetails.listingId, id))
          .limit(1);

        if (existingDetails.length > 0) {
          // Update existing details
          [details] = await tx
            .update(listingDetails)
            .set({
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails,
              operations: data.details.operations,
              growthOpportunities: data.details.growthOpportunities,
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable,
              equipmentHighlights: data.details.equipmentHighlights,
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails,
              updatedAt: now,
            })
            .where(eq(listingDetails.listingId, id))
            .returning();
        } else {
          // Create new details
          [details] = await tx
            .insert(listingDetails)
            .values({
              listingId: id,
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails || {},
              operations: data.details.operations || {},
              growthOpportunities: data.details.growthOpportunities || [],
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable || false,
              equipmentHighlights: data.details.equipmentHighlights || [],
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails || {},
              createdAt: now,
              updatedAt: now,
            })
            .returning();
        }
      }

      // Return response format that matches expectations
      const response = {
        ...updatedListing,
        details,
      };

      // If status changed, include status change info like the old updateListingStatus method
      if (statusChange) {
        return {
          listing: response,
          status_change: statusChange,
        };
      }

      return response;
    });
  }

  /**
   * Update listing status with history tracking
   */
  static async updateListingStatus(id: string, statusData: StatusChangeData): Promise<any> {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Get current listing
      const [currentListing] = await tx
        .select()
        .from(listings)
        .where(and(eq(listings.id, id), eq(listings.organizationId, statusData.organizationId)))
        .limit(1);

      if (!currentListing) {
        throw new HTTPException(404, { message: "Listing not found" });
      }

      const fromStatus = currentListing.status;

      // Update listing status
      const [updatedListing] = await tx
        .update(listings)
        .set({
          status: statusData.status,
          updatedAt: now,
        })
        .where(eq(listings.id, id))
        .returning();

      // Log status change
      const [statusChange] = await tx
        .insert(listingStatusHistory)
        .values({
          listingId: id,
          organizationId: statusData.organizationId,
          changedBy: statusData.changedBy,
          fromStatus: fromStatus,
          toStatus: statusData.status,
          reason: statusData.reason,
          notes: statusData.notes,
          createdAt: now,
        })
        .returning();

      return {
        listing: updatedListing,
        status_change: statusChange,
      };
    });
  }

  /**
   * Delete a listing
   */
  static async deleteListing(id: string, organizationId: string): Promise<void> {
    const result = await db
      .delete(listings)
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
      .execute();

    // Check if any rows were affected by checking if we can find the listing
    const check = await db
      .select({ id: listings.id })
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
      .limit(1);

    if (check.length > 0) {
      throw new HTTPException(500, { message: "Failed to delete listing" });
    }
  }

  /**
   * Bulk create listings from CSV import - HIGH-LATENCY OPTIMIZED VERSION
   * Optimized for remote databases with high connection latency
   */
  static async bulkCreateListings(listingsData: CreateListingData[]): Promise<any> {
    // For high-latency databases, use larger batches to minimize round trips
    const BATCH_SIZE = Math.min(1000, listingsData.length); // Use larger batches
    
    // Always use single batch for small datasets to minimize connection overhead
    if (listingsData.length <= BATCH_SIZE) {
      return await this.processSingleBatch(listingsData);
    }

    // For larger datasets, still batch but with larger sizes
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    for (let i = 0; i < listingsData.length; i += BATCH_SIZE) {
      const batch = listingsData.slice(i, i + BATCH_SIZE);
      const batchResults = await this.processBatch(batch, i);
      
      results.created.push(...batchResults.created);
      results.failed.push(...batchResults.failed);
    }

    return results;
  }

  /**
   * Process single batch without validation overhead for small datasets
   */
  private static async processSingleBatch(listingsData: CreateListingData[]): Promise<any> {
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    try {
      // Quick validation - fail fast
      for (let i = 0; i < listingsData.length; i++) {
        const data = listingsData[i];
        if (!data.businessName || !data.industry) {
          results.failed.push({
            index: i,
            error: 'Missing required fields: businessName or industry',
            data: data,
          });
          listingsData.splice(i, 1);
          i--; // Adjust index after splice
        }
      }

      // Bulk insert all valid records
      if (listingsData.length > 0) {
        const batchResult = await this.bulkInsertListings(listingsData);
        for (let i = 0; i < batchResult.count; i++) {
          results.created.push({ success: true });
        }
      }
    } catch (error) {
      // If anything fails, mark all remaining as failed
      listingsData.forEach((data, index) => {
        results.failed.push({
          index,
          error: error instanceof Error ? error.message : 'Bulk insert failed',
          data: data,
        });
      });
    }

    return results;
  }

  /**
   * Process a batch of listings with optimized bulk operations
   */
  private static async processBatch(batch: CreateListingData[], startIndex: number): Promise<any> {
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    // Pre-validate all records in the batch before any database operations
    const validRecords: CreateListingData[] = [];
    const invalidRecords: any[] = [];

    for (let i = 0; i < batch.length; i++) {
      try {
        const data = batch[i];
        // Basic validation
        if (!data.businessName || !data.industry) {
          throw new Error('Missing required fields: businessName or industry');
        }
        validRecords.push(data);
      } catch (error) {
        invalidRecords.push({
          index: startIndex + i,
          error: error instanceof Error ? error.message : 'Validation error',
          data: batch[i],
        });
      }
    }

    // Add failed validations to results
    results.failed.push(...invalidRecords);

    // Process valid records in bulk
    if (validRecords.length > 0) {
      const batchResult = await this.bulkInsertListings(validRecords);
      // Add the count to created results (without detailed records)
      for (let i = 0; i < batchResult.count; i++) {
        results.created.push({ success: true });
      }
    }

    return results;
  }

  /**
   * Ultra-fast bulk insert - minimal fields and optimized SQL
   */
  private static async bulkInsertListings(listingsData: CreateListingData[]): Promise<{ count: number }> {
    // Use minimal fields to reduce index overhead
    const insertCount = await db.transaction(async (tx) => {
      const now = new Date().toISOString();
      
      // Prepare minimal data with only required fields
      const values = listingsData.map(data => ({
        organizationId: data.organizationId,
        createdBy: data.createdBy,
        businessName: data.businessName,
        industry: data.industry,
        status: this.validateStatus(data.status),
        listingType: 'business_sale',
        teamVisibility: 'all',
        title: data.title || data.businessName,
        createdAt: now,
        updatedAt: now,
      }));

      // Single bulk insert with minimal fields
      await tx.insert(listings).values(values);

      return values.length;
    });

    return { count: insertCount };
  }

  /**
   * Validate and preview CSV data without saving to database
   */
  static async validateCsvData(file: File, organizationId: string, createdBy: string): Promise<any> {
    // Parse CSV file
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFile(file);

    // If there are validation errors, return them
    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV validation errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }

    const results = {
      preview: [] as any[],
      valid: [] as any[],
      invalid: [] as any[],
      summary: {
        total_rows: parsedData.data.length,
        valid_rows: 0,
        invalid_rows: 0,
        required_fields: ['business_name', 'industry'],
        optional_fields: [
          'asking_price', 'cash_flow_sde', 'annual_revenue', 'status', 
          'general_location', 'year_established', 'employees', 'owner_hours_week',
          'date_listed', 'brief_description', 'reason_for_sale', 'growth_opportunities'
        ]
      }
    };

    // Process each row for validation and preview
    for (let i = 0; i < parsedData.data.length; i++) {
      try {
        const csvRow = parsedData.data[i];
        const listingData = CsvParser.csvRowToListingData(csvRow, organizationId, createdBy);
        
        // Additional business logic validation
        const validationErrors: string[] = [];
        
        // Validate asking price if provided
        if (listingData.asking_price !== undefined && listingData.asking_price <= 0) {
          validationErrors.push('Asking price must be greater than 0');
        }
        
        // Validate year established
        if (listingData.year_established !== undefined) {
          const currentYear = new Date().getFullYear();
          if (listingData.year_established < 1800 || listingData.year_established > currentYear) {
            validationErrors.push(`Year established must be between 1800 and ${currentYear}`);
          }
        }
        
        // Validate employee count
        if (listingData.employees !== undefined && listingData.employees < 0) {
          validationErrors.push('Number of employees cannot be negative');
        }
        
        // Validate owner hours per week
        if (listingData.owner_hours_week !== undefined && (listingData.owner_hours_week < 0 || listingData.owner_hours_week > 168)) {
          validationErrors.push('Owner hours per week must be between 0 and 168');
        }

        if (validationErrors.length > 0) {
          results.invalid.push({
            row: i + 2, // +2 because index is 0-based and we skip header
            errors: validationErrors,
            data: listingData,
            original_csv_data: csvRow
          });
          results.summary.invalid_rows++;
        } else {
          results.valid.push({
            row: i + 2,
            data: listingData,
            original_csv_data: csvRow
          });
          results.summary.valid_rows++;
        }

        // Add to preview (first 10 rows for display)
        if (results.preview.length < 10) {
          results.preview.push({
            row: i + 2,
            business_name: listingData.business_name,
            industry: listingData.industry,
            asking_price: listingData.asking_price,
            status: listingData.status || 'draft',
            location: listingData.general_location,
            validation_status: validationErrors.length > 0 ? 'invalid' : 'valid',
            errors: validationErrors
          });
        }

      } catch (error) {
        results.invalid.push({
          row: i + 2,
          errors: [error instanceof Error ? error.message : 'Unknown parsing error'],
          data: parsedData.data[i],
          original_csv_data: parsedData.data[i]
        });
        results.summary.invalid_rows++;
      }
    }

    return results;
  }

  /**
   * Bulk create listings from CSV file - ULTRA-HIGH-PERFORMANCE VERSION
   * Optimized for minimal processing time with streamlined operations
   */
  static async bulkCreateListingsFromCsv(file: File, organizationId: string, createdBy: string): Promise<any> {
    const startTime = Date.now();
    
    // Use streamlined CSV parser for better performance
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFileStreamlined(file);

    // If there are parsing errors, return them
    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV parsing errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }

    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    // Convert all CSV data to listing format in single pass using streamlined converter
    const validListingsData: CreateListingData[] = [];
    
    parsedData.data.forEach((csvRow, index) => {
      try {
        const listingData = CsvParser.csvRowToListingDataStreamlined(csvRow, organizationId, createdBy);
        validListingsData.push(listingData);
      } catch (error) {
        results.failed.push({
          index,
          error: error instanceof Error ? error.message : 'Data conversion error',
          data: csvRow,
        });
      }
    });

    // Use the optimized bulk creation method
    if (validListingsData.length > 0) {
      const bulkResults = await this.bulkCreateListings(validListingsData);
      results.created.push(...bulkResults.created);
      results.failed.push(...bulkResults.failed);
    }

    const endTime = Date.now();
    console.log(`CSV import completed in ${endTime - startTime}ms for ${parsedData.data.length} rows`);

    return results;
  }

  /**
   * Advanced bulk import with progress tracking and memory optimization
   * Suitable for very large CSV files that need progress reporting
   */
  static async bulkCreateListingsFromCsvWithProgress(
    file: File, 
    organizationId: string, 
    createdBy: string,
    progressCallback?: (progress: { processed: number; total: number; created: number; failed: number }) => void
  ): Promise<any> {
    // Parse CSV file
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFile(file);

    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV parsing errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }

    const BATCH_SIZE = 100;
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    const totalRows = parsedData.data.length;
    let processedRows = 0;

    // Process in batches with progress tracking
    for (let i = 0; i < parsedData.data.length; i += BATCH_SIZE) {
      const batch = parsedData.data.slice(i, i + BATCH_SIZE);
      
      // Convert CSV rows to listing data
      const batchListingsData: CreateListingData[] = [];
      const batchConversionErrors: any[] = [];

      for (let j = 0; j < batch.length; j++) {
        try {
          const csvRow = batch[j];
          const listingData = CsvParser.csvRowToListingData(csvRow, organizationId, createdBy);
          
          if (!listingData.businessName || !listingData.industry) {
            throw new Error('Missing required fields');
          }
          
          batchListingsData.push(listingData);
        } catch (error) {
          batchConversionErrors.push({
            index: i + j,
            error: error instanceof Error ? error.message : 'Data conversion error',
            data: batch[j],
          });
        }
      }

      // Add conversion errors
      results.failed.push(...batchConversionErrors);

      // Process valid batch data
      if (batchListingsData.length > 0) {
        const batchResult = await this.bulkInsertListings(batchListingsData);
        // Add the count to created results (without detailed records)
        for (let i = 0; i < batchResult.count; i++) {
          results.created.push({ success: true });
        }
      }

      processedRows += batch.length;

      // Report progress if callback provided
      if (progressCallback) {
        progressCallback({
          processed: processedRows,
          total: totalRows,
          created: results.created.length,
          failed: results.failed.length,
        });
      }

      // Yield control to event loop to prevent blocking
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    return results;
  }

  /**
   * Get listing status history
   */
  static async getListingStatusHistory(listingId: string, organizationId: string) {
    const history = await db
      .select({
        id: listingStatusHistory.id,
        listingId: listingStatusHistory.listingId,
        organizationId: listingStatusHistory.organizationId,
        changedBy: listingStatusHistory.changedBy,
        fromStatus: listingStatusHistory.fromStatus,
        toStatus: listingStatusHistory.toStatus,
        reason: listingStatusHistory.reason,
        notes: listingStatusHistory.notes,
        metadata: listingStatusHistory.metadata,
        createdAt: listingStatusHistory.createdAt,
        // Join with user profiles to get the name of who made the change
        changedByName: userProfiles.displayName,
      })
      .from(listingStatusHistory)
      .leftJoin(userProfiles, eq(listingStatusHistory.changedBy, userProfiles.userId))
      .where(
        and(
          eq(listingStatusHistory.listingId, listingId),
          eq(listingStatusHistory.organizationId, organizationId)
        )
      )
      .orderBy(desc(listingStatusHistory.createdAt));

    return history;
  }

  /**
   * Get all notes for a listing
   */
  static async getListingNotes(listingId: string, organizationId: string) {
    const notes = await db
      .select({
        id: listingNotes.id,
        listingId: listingNotes.listingId,
        organizationId: listingNotes.organizationId,
        createdBy: listingNotes.createdBy,
        content: listingNotes.content,
        mentions: listingNotes.mentions,
        isPrivate: listingNotes.isPrivate,
        createdAt: listingNotes.createdAt,
        updatedAt: listingNotes.updatedAt,
        // Join with user profiles to get the name of who created the note
        createdByName: userProfiles.displayName,
      })
      .from(listingNotes)
      .leftJoin(userProfiles, eq(listingNotes.createdBy, userProfiles.userId))
      .where(
        and(
          eq(listingNotes.listingId, listingId),
          eq(listingNotes.organizationId, organizationId)
        )
      )
      .orderBy(desc(listingNotes.createdAt));

    return notes;
  }

  /**
   * Create a new note for a listing
   */
  static async createListingNote(data: CreateListingNoteData) {
    const now = new Date().toISOString();

    const [note] = await db
      .insert(listingNotes)
      .values({
        listingId: data.listingId,
        organizationId: data.organizationId,
        createdBy: data.createdBy,
        content: data.content,
        mentions: data.mentions || [],
        isPrivate: data.isPrivate || false,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    // Get the note with creator name
    const [noteWithCreator] = await db
      .select({
        id: listingNotes.id,
        listingId: listingNotes.listingId,
        organizationId: listingNotes.organizationId,
        createdBy: listingNotes.createdBy,
        content: listingNotes.content,
        mentions: listingNotes.mentions,
        isPrivate: listingNotes.isPrivate,
        createdAt: listingNotes.createdAt,
        updatedAt: listingNotes.updatedAt,
        createdByName: userProfiles.displayName,
      })
      .from(listingNotes)
      .leftJoin(userProfiles, eq(listingNotes.createdBy, userProfiles.userId))
      .where(eq(listingNotes.id, note.id))
      .limit(1);

    return noteWithCreator;
  }

  /**
   * Update an existing note
   */
  static async updateListingNote(
    noteId: string,
    data: UpdateListingNoteData,
    organizationId: string,
    userId: string
  ) {
    const now = new Date().toISOString();

    // Check if note exists and user has permission to update it
    const [existingNote] = await db
      .select()
      .from(listingNotes)
      .where(
        and(
          eq(listingNotes.id, noteId),
          eq(listingNotes.organizationId, organizationId)
        )
      )
      .limit(1);

    if (!existingNote) {
      throw new HTTPException(404, { message: "Note not found" });
    }

    // Only the creator can update their own note
    if (existingNote.createdBy !== userId) {
      throw new HTTPException(403, { message: "You can only update your own notes" });
    }

    const updateData: any = {
      updatedAt: now,
    };

    if (data.content !== undefined) updateData.content = data.content;
    if (data.mentions !== undefined) updateData.mentions = data.mentions;
    if (data.isPrivate !== undefined) updateData.isPrivate = data.isPrivate;

    const [updatedNote] = await db
      .update(listingNotes)
      .set(updateData)
      .where(eq(listingNotes.id, noteId))
      .returning();

    // Get the updated note with creator name
    const [noteWithCreator] = await db
      .select({
        id: listingNotes.id,
        listingId: listingNotes.listingId,
        organizationId: listingNotes.organizationId,
        createdBy: listingNotes.createdBy,
        content: listingNotes.content,
        mentions: listingNotes.mentions,
        isPrivate: listingNotes.isPrivate,
        createdAt: listingNotes.createdAt,
        updatedAt: listingNotes.updatedAt,
        createdByName: userProfiles.displayName,
      })
      .from(listingNotes)
      .leftJoin(userProfiles, eq(listingNotes.createdBy, userProfiles.userId))
      .where(eq(listingNotes.id, noteId))
      .limit(1);

    return noteWithCreator;
  }

  /**
   * Delete a note
   */
  static async deleteListingNote(noteId: string, organizationId: string, userId: string) {
    // Check if note exists and user has permission to delete it
    const [existingNote] = await db
      .select()
      .from(listingNotes)
      .where(
        and(
          eq(listingNotes.id, noteId),
          eq(listingNotes.organizationId, organizationId)
        )
      )
      .limit(1);

    if (!existingNote) {
      throw new HTTPException(404, { message: "Note not found" });
    }

    // Only the creator can delete their own note
    if (existingNote.createdBy !== userId) {
      throw new HTTPException(403, { message: "You can only delete your own notes" });
    }

    await db
      .delete(listingNotes)
      .where(eq(listingNotes.id, noteId));
  }

  /**
   * Performance monitoring for bulk import operations
   * Provides detailed timing and memory usage statistics
   */
  static async bulkImportWithPerformanceMetrics(
    file: File,
    organizationId: string,
    createdBy: string
  ): Promise<{
    results: any;
    performance: {
      totalTime: number;
      parseTime: number;
      processTime: number;
      recordsPerSecond: number;
      memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        external: number;
      };
    };
  }> {
    const startTime = Date.now();
    let parseEndTime: number;
    
    // Monitor initial memory usage
    const initialMemory = process.memoryUsage();
    
    console.log(`Starting bulk import for file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
    
    // Parse CSV with timing
    const parseStartTime = Date.now();
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFile(file);
    parseEndTime = Date.now();
    
    console.log(`CSV parsing completed in ${parseEndTime - parseStartTime}ms for ${parsedData.data.length} rows`);
    
    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV parsing errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }
    
    // Process data with timing and progress tracking
    const processStartTime = Date.now();
    
    const results = await this.bulkCreateListingsFromCsvWithProgress(
      file,
      organizationId,
      createdBy,
      (progress) => {
        const progressPercent = ((progress.processed / progress.total) * 100).toFixed(1);
        console.log(`Import progress: ${progressPercent}% (${progress.processed}/${progress.total}) - Created: ${progress.created}, Failed: ${progress.failed}`);
      }
    );
    
    const processEndTime = Date.now();
    const totalEndTime = Date.now();
    
    // Calculate performance metrics
    const totalTime = totalEndTime - startTime;
    const parseTime = parseEndTime - parseStartTime;
    const processTime = processEndTime - processStartTime;
    const recordsPerSecond = Math.round((results.created.length / (totalTime / 1000)) * 100) / 100;
    
    // Monitor final memory usage
    const finalMemory = process.memoryUsage();
    
    const performanceMetrics = {
      totalTime,
      parseTime,
      processTime,
      recordsPerSecond,
      memoryUsage: {
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        heapTotal: finalMemory.heapTotal,
        external: finalMemory.external,
      },
    };
    
    console.log('Bulk import completed with performance metrics:', {
      ...performanceMetrics,
      created: results.created.length,
      failed: results.failed.length,
      successRate: `${((results.created.length / (results.created.length + results.failed.length)) * 100).toFixed(1)}%`
    });
    
    return {
      results,
      performance: performanceMetrics,
    };
  }



  /**
   * Get listings analytics/statistics
   */
  static async getListingStats(organizationId: string) {
    const [stats] = await db
      .select({
        total: count(),
        active: count(sql`CASE WHEN ${listings.status} = 'active' THEN 1 END`),
        pending: count(sql`CASE WHEN ${listings.status} = 'pending' THEN 1 END`),
        sold: count(sql`CASE WHEN ${listings.status} = 'sold' THEN 1 END`),
        avgAskingPrice: sql<number>`avg(${listings.askingPrice}::numeric)`,
        avgDaysListed: sql<number>`avg(${listings.daysListed})`,
      })
      .from(listings)
      .where(eq(listings.organizationId, organizationId));

    return stats;
  }




  // static async AIQuarterlyReport(listingId: string, organizationId: string): Promise<string> {
  //   try {
      // Get the complete listing data
//       const listing = await this.getListingById(listingId, organizationId, true);
//       const systemPrompt = `You are a business broker expert. Analyze this business listing and provide insights about market positioning, valuation, opportunities, and recommendations.`;
//       const userInput = `Analyze this business listing:
// Business: ${listing.businessName}
// Industry: ${listing.industry}
// Asking Price: ${listing.askingPrice ? `$${parseFloat(listing.askingPrice).toLocaleString()}` : 'Not specified'}
// Annual Revenue: ${listing.annualRevenue ? `$${parseFloat(listing.annualRevenue).toLocaleString()}` : 'Not specified'}`

//       const aiResponse = await OpenAIService.chatCompletion({
//         systemPrompt,
//         userInput,
//         model: "gpt-4o-mini",
//       });

//       return aiResponse.content;
//     } catch (error) {
//       if (error instanceof Error && error.message.includes('OpenAI service is not available')) {
//         throw new HTTPException(503, { message: "AI features are not available. OpenAI API key is not configured." });
//       }
//       throw error;
    // }
  // }

}