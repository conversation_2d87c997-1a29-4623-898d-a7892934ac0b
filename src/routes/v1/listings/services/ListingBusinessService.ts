import { eq, and, desc } from "drizzle-orm";
import db from "@/db";
import { listingStatusHistory, listingNotes, userProfiles } from "@/db/schema";

export interface StatusChangeData {
  status: string;
  reason?: string;
  notes?: string;
  changedBy: string;
  organizationId: string;
}

export interface CreateListingNoteData {
  listingId: string;
  organizationId: string;
  createdBy: string;
  content: string;
  mentions?: string[];
  isPrivate?: boolean;
}

/**
 * ListingBusinessService - Business rules and status management
 * 
 * This service handles:
 * - Status transitions and business rules
 * - Status history tracking
 * - Listing notes management
 * - Business logic operations
 * - Workflow management
 */
export class ListingBusinessService {
  /**
   * Record a status change in the history
   */
  static async recordStatusChange(listingId: string, statusData: StatusChangeData) {
    const now = new Date().toISOString();

    const [statusRecord] = await db
      .insert(listingStatusHistory)
      .values({
        listingId: listingId,
        organizationId: statusData.organizationId,
        changedBy: statusData.changedBy,
        fromStatus: null, // Will be set by the caller if known
        toStatus: statusData.status,
        reason: statusData.reason || null,
        notes: statusData.notes || null,
        metadata: {},
        createdAt: now,
      })
      .returning();

    return statusRecord;
  }

  /**
   * Record a status transition with from/to status
   */
  static async recordStatusTransition(
    listingId: string, 
    fromStatus: string | null, 
    toStatus: string, 
    changedBy: string, 
    organizationId: string,
    reason?: string,
    notes?: string
  ) {
    const now = new Date().toISOString();

    const [statusRecord] = await db
      .insert(listingStatusHistory)
      .values({
        listingId: listingId,
        organizationId: organizationId,
        changedBy: changedBy,
        fromStatus: fromStatus,
        toStatus: toStatus,
        reason: reason || null,
        notes: notes || null,
        metadata: {},
        createdAt: now,
      })
      .returning();

    return statusRecord;
  }

  /**
   * Get listing status history
   */
  static async getStatusHistory(listingId: string, organizationId: string) {
    const history = await db
      .select({
        id: listingStatusHistory.id,
        listingId: listingStatusHistory.listingId,
        organizationId: listingStatusHistory.organizationId,
        changedBy: listingStatusHistory.changedBy,
        fromStatus: listingStatusHistory.fromStatus,
        toStatus: listingStatusHistory.toStatus,
        reason: listingStatusHistory.reason,
        notes: listingStatusHistory.notes,
        metadata: listingStatusHistory.metadata,
        createdAt: listingStatusHistory.createdAt,
        // Join with user profiles to get the name of who made the change
        changedByName: userProfiles.displayName,
      })
      .from(listingStatusHistory)
      .leftJoin(userProfiles, eq(listingStatusHistory.changedBy, userProfiles.userId))
      .where(
        and(
          eq(listingStatusHistory.listingId, listingId),
          eq(listingStatusHistory.organizationId, organizationId)
        )
      )
      .orderBy(desc(listingStatusHistory.createdAt));

    return history;
  }

  /**
   * Create a listing note
   */
  static async createNote(data: CreateListingNoteData) {
    const now = new Date().toISOString();

    const [note] = await db
      .insert(listingNotes)
      .values({
        listingId: data.listingId,
        organizationId: data.organizationId,
        createdBy: data.createdBy,
        content: data.content,
        mentions: data.mentions || [],
        isPrivate: data.isPrivate || false,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    return note;
  }

  /**
   * Get all notes for a listing
   */
  static async getNotes(listingId: string, organizationId: string) {
    const notes = await db
      .select({
        id: listingNotes.id,
        listingId: listingNotes.listingId,
        organizationId: listingNotes.organizationId,
        createdBy: listingNotes.createdBy,
        content: listingNotes.content,
        mentions: listingNotes.mentions,
        isPrivate: listingNotes.isPrivate,
        createdAt: listingNotes.createdAt,
        updatedAt: listingNotes.updatedAt,
        // Join with user profiles to get creator name
        createdByName: userProfiles.displayName,
      })
      .from(listingNotes)
      .leftJoin(userProfiles, eq(listingNotes.createdBy, userProfiles.userId))
      .where(
        and(
          eq(listingNotes.listingId, listingId),
          eq(listingNotes.organizationId, organizationId)
        )
      )
      .orderBy(desc(listingNotes.createdAt));

    return notes;
  }

  /**
   * Update a listing note
   */
  static async updateNote(
    noteId: string, 
    data: Partial<CreateListingNoteData>, 
    organizationId: string, 
    userId: string
  ) {
    const now = new Date().toISOString();

    const updateData: any = {
      updatedAt: now,
    };

    if (data.content !== undefined) updateData.content = data.content;
    if (data.mentions !== undefined) updateData.mentions = data.mentions;
    if (data.isPrivate !== undefined) updateData.isPrivate = data.isPrivate;

    const [updatedNote] = await db
      .update(listingNotes)
      .set(updateData)
      .where(eq(listingNotes.id, noteId))
      .returning();

    return updatedNote;
  }

  /**
   * Delete a listing note
   */
  static async deleteNote(noteId: string, organizationId: string, userId: string) {
    await db
      .delete(listingNotes)
      .where(
        and(
          eq(listingNotes.id, noteId),
          eq(listingNotes.organizationId, organizationId),
          eq(listingNotes.createdBy, userId) // Only allow creator to delete
        )
      );
  }

  /**
   * Calculate business metrics for a listing
   */
  static calculateBusinessMetrics(listingData: any) {
    const metrics: any = {};

    // Calculate revenue multiple if both revenue and asking price are available
    if (listingData.annualRevenue && listingData.askingPrice) {
      const revenue = parseFloat(listingData.annualRevenue);
      const price = parseFloat(listingData.askingPrice);
      if (revenue > 0) {
        metrics.revenueMultiple = price / revenue;
      }
    }

    // Calculate cash flow multiple if both cash flow and asking price are available
    if (listingData.cashFlowSde && listingData.askingPrice) {
      const cashFlow = parseFloat(listingData.cashFlowSde);
      const price = parseFloat(listingData.askingPrice);
      if (cashFlow > 0) {
        metrics.cashFlowMultiple = price / cashFlow;
      }
    }

    // Calculate revenue per employee if both are available
    if (listingData.annualRevenue && listingData.employees) {
      const revenue = parseFloat(listingData.annualRevenue);
      const employees = parseInt(listingData.employees);
      if (employees > 0) {
        metrics.revenuePerEmployee = revenue / employees;
      }
    }

    // Calculate days on market if date listed is available
    if (listingData.dateListed) {
      const listedDate = new Date(listingData.dateListed);
      const currentDate = new Date();
      const timeDiff = currentDate.getTime() - listedDate.getTime();
      metrics.daysOnMarket = Math.floor(timeDiff / (1000 * 3600 * 24));
    }

    return metrics;
  }

  /**
   * Determine if a listing is ready for publication
   */
  static isReadyForPublication(listingData: any): { ready: boolean; missingFields: string[] } {
    const requiredFields = [
      'businessName',
      'industry',
      'askingPrice',
      'generalLocation',
      'description'
    ];

    const missingFields = requiredFields.filter(field => {
      const value = listingData[field];
      return !value || (typeof value === 'string' && value.trim().length === 0);
    });

    return {
      ready: missingFields.length === 0,
      missingFields
    };
  }

  /**
   * Generate listing summary for reports
   */
  static generateListingSummary(listingData: any) {
    const metrics = this.calculateBusinessMetrics(listingData);
    const publicationStatus = this.isReadyForPublication(listingData);

    return {
      id: listingData.id,
      businessName: listingData.businessName,
      industry: listingData.industry,
      status: listingData.status,
      askingPrice: listingData.askingPrice,
      metrics,
      publicationStatus,
      createdAt: listingData.createdAt,
      updatedAt: listingData.updatedAt,
    };
  }

  /**
   * Get listing activity summary
   */
  static async getListingActivity(listingId: string, organizationId: string) {
    // Get recent status changes
    const recentStatusChanges = await db
      .select({
        id: listingStatusHistory.id,
        fromStatus: listingStatusHistory.fromStatus,
        toStatus: listingStatusHistory.toStatus,
        changedBy: listingStatusHistory.changedBy,
        createdAt: listingStatusHistory.createdAt,
        changedByName: userProfiles.displayName,
      })
      .from(listingStatusHistory)
      .leftJoin(userProfiles, eq(listingStatusHistory.changedBy, userProfiles.userId))
      .where(
        and(
          eq(listingStatusHistory.listingId, listingId),
          eq(listingStatusHistory.organizationId, organizationId)
        )
      )
      .orderBy(desc(listingStatusHistory.createdAt))
      .limit(5);

    // Get recent notes
    const recentNotes = await db
      .select({
        id: listingNotes.id,
        content: listingNotes.content,
        createdBy: listingNotes.createdBy,
        createdAt: listingNotes.createdAt,
        createdByName: userProfiles.displayName,
      })
      .from(listingNotes)
      .leftJoin(userProfiles, eq(listingNotes.createdBy, userProfiles.userId))
      .where(
        and(
          eq(listingNotes.listingId, listingId),
          eq(listingNotes.organizationId, organizationId)
        )
      )
      .orderBy(desc(listingNotes.createdAt))
      .limit(5);

    return {
      statusChanges: recentStatusChanges,
      notes: recentNotes,
    };
  }
}
