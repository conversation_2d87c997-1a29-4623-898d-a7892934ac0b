import { eq, and } from "drizzle-orm";
import db from "@/db";
import { listings, listingDetails } from "@/db/schema";
import type { CreateListingData, SaveDraftListingData, UpdateListingData } from "../listings.service";

/**
 * ListingDataService - Pure CRUD operations for listings
 * 
 * This service handles basic database operations without business logic:
 * - Create, read, update, delete operations
 * - Database queries and data retrieval
 * - No orchestration or complex business rules
 */
export class ListingDataService {
  /**
   * Get a single listing by ID and organization
   */
  static async getById(id: string, organizationId: string, includeDetails = true) {
    const listing = await db
      .select()
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
      .limit(1);

    if (listing.length === 0) {
      return null;
    }

    let details = null;
    if (includeDetails) {
      const detailsResult = await db
        .select()
        .from(listingDetails)
        .where(eq(listingDetails.listingId, id))
        .limit(1);

      details = detailsResult[0] || null;
    }

    const listingData = listing[0];

    // If status is 'draft' and _draft data exists, merge it with the base data
    if (listingData.status === 'draft' && listingData._draft) {
      const draftData = listingData._draft as any;
      return {
        ...draftData,
        // Keep system fields from the original listing
        id: listingData.id,
        organizationId: listingData.organizationId,
        createdBy: listingData.createdBy,
        status: listingData.status,
        createdAt: listingData.createdAt,
        updatedAt: listingData.updatedAt,
        details,
      };
    }

    return {
      ...listingData,
      details,
    };
  }

  /**
   * Create a new listing record
   */
  static async create(data: CreateListingData) {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Create the main listing record
      const [listing] = await tx
        .insert(listings)
        .values({
          organizationId: data.organizationId,
          createdBy: data.createdBy,
          assignedTo: data.assignedTo || null,
          businessName: data.businessName,
          industry: data.industry,
          askingPrice: data.askingPrice?.toString() || null,
          cashFlowSde: data.cashFlowSde?.toString() || null,
          annualRevenue: data.annualRevenue?.toString() || null,
          status: 'active',
          generalLocation: data.generalLocation || null,
          yearEstablished: data.yearEstablished || null,
          employees: data.employees || null,
          ownerHoursWeek: data.ownerHoursWeek || null,
          dateListed: data.dateListed || null,
          daysListed: data.dateListed ? this.calculateDaysListed(data.dateListed) : null,
          title: data.title || null,
          description: data.description || null,
          price: data.price?.toString() || null,
          address: data.address || null,
          city: data.city || null,
          state: data.state || null,
          zipCode: data.zipCode || null,
          propertyType: data.propertyType || null,
          squareFootage: data.squareFootage || null,
          lotSize: data.lotSize?.toString() || null,
          yearBuilt: data.yearBuilt || null,
          bedrooms: data.bedrooms || null,
          bathrooms: data.bathrooms?.toString() || null,
          listingType: data.listingType || 'business_sale',
          teamVisibility: data.teamVisibility || 'all',
          internalNotes: data.internalNotes || [],
          photos: data.photos || [],
          documents: data.documents || [],
          featuredPhoto: data.featuredPhoto || null,
          virtualTourUrl: data.virtualTourUrl || null,
          mlsNumber: data.mlsNumber || null,
          listingDate: data.listingDate || null,
          expirationDate: data.expirationDate || null,
          // daysOnMarket: calculated field, not from input
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      // Create details if provided
      if (data.details) {
        await tx
          .insert(listingDetails)
          .values({
            listingId: listing.id,
            businessDescription: data.details.businessDescription || null,
            briefDescription: data.details.briefDescription || null,
            financialDetails: data.details.financialDetails || null,
            operations: data.details.operations || null,
            growthOpportunities: data.details.growthOpportunities || null,
            reasonForSale: data.details.reasonForSale || null,
            trainingPeriod: data.details.trainingPeriod || null,
            supportType: data.details.supportType || null,
            financingAvailable: data.details.financingAvailable || false,
            equipmentHighlights: data.details.equipmentHighlights || null,
            supplierRelationships: data.details.supplierRelationships || null,
            realEstateStatus: data.details.realEstateStatus || null,
            leaseDetails: data.details.leaseDetails || null,
            createdAt: now,
            updatedAt: now,
          });
      }

      return listing;
    });
  }

  /**
   * Create a draft listing
   */
  static async createDraft(data: SaveDraftListingData) {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Create the main listing record with minimal required fields
      const [listing] = await tx
        .insert(listings)
        .values({
          organizationId: data.organizationId,
          createdBy: data.createdBy,
          // Use placeholder values for required fields if not provided
          businessName: data.businessName || 'Draft Listing',
          industry: data.industry || 'Not Specified',
          status: 'draft',
          teamVisibility: data.teamVisibility || 'all',
          listingType: data.listingType || 'business_sale',
          // Store all provided data in _draft column
          _draft: data,
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      return listing;
    });
  }

  /**
   * Update a listing record
   */
  static async update(id: string, organizationId: string, data: UpdateListingData) {
    const now = new Date().toISOString();

    const updateData: any = {
      updatedAt: now,
    };

    // Only update fields that are provided
    if (data.businessName !== undefined) updateData.businessName = data.businessName;
    if (data.industry !== undefined) updateData.industry = data.industry;
    if (data.askingPrice !== undefined) updateData.askingPrice = data.askingPrice?.toString();
    if (data.cashFlowSde !== undefined) updateData.cashFlowSde = data.cashFlowSde?.toString();
    if (data.annualRevenue !== undefined) updateData.annualRevenue = data.annualRevenue?.toString();
    if (data.status !== undefined) updateData.status = data.status;
    if (data.generalLocation !== undefined) updateData.generalLocation = data.generalLocation;
    if (data.yearEstablished !== undefined) updateData.yearEstablished = data.yearEstablished;
    if (data.employees !== undefined) updateData.employees = data.employees;
    if (data.ownerHoursWeek !== undefined) updateData.ownerHoursWeek = data.ownerHoursWeek;
    if (data.dateListed !== undefined) {
      updateData.dateListed = data.dateListed;
      updateData.daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : null;
    }
    if (data.assignedTo !== undefined) updateData.assignedTo = data.assignedTo;

    const [updatedListing] = await db
      .update(listings)
      .set(updateData)
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
      .returning();

    return updatedListing;
  }

  /**
   * Update draft data for a listing
   */
  static async updateDraft(id: string, organizationId: string, data: SaveDraftListingData) {
    const now = new Date().toISOString();

    // Get existing listing to preserve system fields
    const existingListing = await this.getById(id, organizationId, false);
    if (!existingListing) {
      return null;
    }

    // Merge new data with existing draft data
    const existingDraftData = (existingListing._draft as any) || {};
    const mergedDraftData = {
      ...existingDraftData,
      ...data,
      // Preserve system fields
      organizationId: data.organizationId,
      createdBy: existingListing.createdBy, // Don't allow changing the creator
    };

    const [updatedListing] = await db
      .update(listings)
      .set({
        _draft: mergedDraftData,
        updatedAt: now,
      })
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)))
      .returning();

    return updatedListing;
  }

  /**
   * Delete a listing
   */
  static async delete(id: string, organizationId: string): Promise<boolean> {
    await db
      .delete(listings)
      .where(and(eq(listings.id, id), eq(listings.organizationId, organizationId)));

    return true; // Drizzle doesn't return rowCount in this context
  }

  /**
   * Calculate days listed from a date
   */
  private static calculateDaysListed(dateListed: string): number {
    const listedDate = new Date(dateListed);
    const currentDate = new Date();
    const timeDiff = currentDate.getTime() - listedDate.getTime();
    return Math.floor(timeDiff / (1000 * 3600 * 24));
  }
}
