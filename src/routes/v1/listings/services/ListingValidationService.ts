import type { CreateListingData, SaveDraftListingData, UpdateListingData } from "../listings.service";

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * ListingValidationService - Data validation logic
 * 
 * This service handles:
 * - Business rule validation
 * - Data format validation
 * - Required field validation
 * - Cross-field validation
 * - Custom business logic validation
 */
export class ListingValidationService {
  /**
   * Validate listing data for creation
   */
  static validateCreateListing(data: CreateListingData): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (!data.businessName || data.businessName.trim().length === 0) {
      errors.push({
        field: 'businessName',
        message: 'Business name is required',
        code: 'REQUIRED_FIELD'
      });
    }

    if (!data.industry || data.industry.trim().length === 0) {
      errors.push({
        field: 'industry',
        message: 'Industry is required',
        code: 'REQUIRED_FIELD'
      });
    }

    if (!data.organizationId || data.organizationId.trim().length === 0) {
      errors.push({
        field: 'organizationId',
        message: 'Organization ID is required',
        code: 'REQUIRED_FIELD'
      });
    }

    if (!data.createdBy || data.createdBy.trim().length === 0) {
      errors.push({
        field: 'createdBy',
        message: 'Created by user ID is required',
        code: 'REQUIRED_FIELD'
      });
    }

    // Business name validation
    if (data.businessName && data.businessName.length > 200) {
      errors.push({
        field: 'businessName',
        message: 'Business name must be 200 characters or less',
        code: 'MAX_LENGTH'
      });
    }

    // Price validation
    if (data.askingPrice !== undefined && data.askingPrice !== null) {
      if (data.askingPrice < 0) {
        errors.push({
          field: 'askingPrice',
          message: 'Asking price must be positive',
          code: 'INVALID_VALUE'
        });
      }
      if (data.askingPrice > 999999999.99) {
        errors.push({
          field: 'askingPrice',
          message: 'Asking price is too large',
          code: 'MAX_VALUE'
        });
      }
    }

    // Cash flow validation
    if (data.cashFlowSde !== undefined && data.cashFlowSde !== null) {
      if (data.cashFlowSde < 0) {
        errors.push({
          field: 'cashFlowSde',
          message: 'Cash flow SDE must be positive',
          code: 'INVALID_VALUE'
        });
      }
    }

    // Revenue validation
    if (data.annualRevenue !== undefined && data.annualRevenue !== null) {
      if (data.annualRevenue < 0) {
        errors.push({
          field: 'annualRevenue',
          message: 'Annual revenue must be positive',
          code: 'INVALID_VALUE'
        });
      }
    }

    // Year established validation
    if (data.yearEstablished !== undefined && data.yearEstablished !== null) {
      const currentYear = new Date().getFullYear();
      if (data.yearEstablished < 1800 || data.yearEstablished > currentYear) {
        errors.push({
          field: 'yearEstablished',
          message: `Year established must be between 1800 and ${currentYear}`,
          code: 'INVALID_RANGE'
        });
      }
    }

    // Employee count validation
    if (data.employees !== undefined && data.employees !== null) {
      if (data.employees < 0) {
        errors.push({
          field: 'employees',
          message: 'Employee count must be positive',
          code: 'INVALID_VALUE'
        });
      }
      if (data.employees > 100000) {
        errors.push({
          field: 'employees',
          message: 'Employee count seems unrealistic',
          code: 'MAX_VALUE'
        });
      }
    }

    // Owner hours validation
    if (data.ownerHoursWeek !== undefined && data.ownerHoursWeek !== null) {
      if (data.ownerHoursWeek < 0 || data.ownerHoursWeek > 168) {
        errors.push({
          field: 'ownerHoursWeek',
          message: 'Owner hours per week must be between 0 and 168',
          code: 'INVALID_RANGE'
        });
      }
    }

    // Listing type validation
    const validListingTypes = ['business_sale', 'franchise', 'asset_sale', 'merger_acquisition'];
    if (data.listingType && !validListingTypes.includes(data.listingType)) {
      errors.push({
        field: 'listingType',
        message: 'Invalid listing type',
        code: 'INVALID_VALUE'
      });
    }

    // Team visibility validation
    const validVisibilities = ['all', 'assigned_only', 'managers_only'];
    if (data.teamVisibility && !validVisibilities.includes(data.teamVisibility)) {
      errors.push({
        field: 'teamVisibility',
        message: 'Invalid team visibility setting',
        code: 'INVALID_VALUE'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate draft listing data
   */
  static validateDraftListing(data: SaveDraftListingData): ValidationResult {
    const errors: ValidationError[] = [];

    // For drafts, only validate required system fields
    if (!data.organizationId || data.organizationId.trim().length === 0) {
      errors.push({
        field: 'organizationId',
        message: 'Organization ID is required',
        code: 'REQUIRED_FIELD'
      });
    }

    if (!data.createdBy || data.createdBy.trim().length === 0) {
      errors.push({
        field: 'createdBy',
        message: 'Created by user ID is required',
        code: 'REQUIRED_FIELD'
      });
    }

    // Validate provided fields if they exist (but don't require them for drafts)
    if (data.askingPrice !== undefined && data.askingPrice !== null && data.askingPrice < 0) {
      errors.push({
        field: 'askingPrice',
        message: 'Asking price must be positive',
        code: 'INVALID_VALUE'
      });
    }

    if (data.yearEstablished !== undefined && data.yearEstablished !== null) {
      const currentYear = new Date().getFullYear();
      if (data.yearEstablished < 1800 || data.yearEstablished > currentYear) {
        errors.push({
          field: 'yearEstablished',
          message: `Year established must be between 1800 and ${currentYear}`,
          code: 'INVALID_RANGE'
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate listing update data
   */
  static validateUpdateListing(data: UpdateListingData): ValidationResult {
    const errors: ValidationError[] = [];

    // Validate only the fields that are being updated
    if (data.businessName !== undefined) {
      if (!data.businessName || data.businessName.trim().length === 0) {
        errors.push({
          field: 'businessName',
          message: 'Business name cannot be empty',
          code: 'REQUIRED_FIELD'
        });
      }
      if (data.businessName && data.businessName.length > 200) {
        errors.push({
          field: 'businessName',
          message: 'Business name must be 200 characters or less',
          code: 'MAX_LENGTH'
        });
      }
    }

    if (data.industry !== undefined) {
      if (!data.industry || data.industry.trim().length === 0) {
        errors.push({
          field: 'industry',
          message: 'Industry cannot be empty',
          code: 'REQUIRED_FIELD'
        });
      }
    }

    if (data.askingPrice !== undefined && data.askingPrice !== null && data.askingPrice < 0) {
      errors.push({
        field: 'askingPrice',
        message: 'Asking price must be positive',
        code: 'INVALID_VALUE'
      });
    }

    if (data.yearEstablished !== undefined && data.yearEstablished !== null) {
      const currentYear = new Date().getFullYear();
      if (data.yearEstablished < 1800 || data.yearEstablished > currentYear) {
        errors.push({
          field: 'yearEstablished',
          message: `Year established must be between 1800 and ${currentYear}`,
          code: 'INVALID_RANGE'
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate status transition
   */
  static validateStatusTransition(fromStatus: string, toStatus: string): ValidationResult {
    const errors: ValidationError[] = [];

    const validStatuses = ['draft', 'active', 'under_contract', 'sold', 'expired', 'withdrawn'];
    
    if (!validStatuses.includes(toStatus)) {
      errors.push({
        field: 'status',
        message: 'Invalid status value',
        code: 'INVALID_VALUE'
      });
    }

    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      'draft': ['active', 'withdrawn'],
      'active': ['under_contract', 'expired', 'withdrawn'],
      'under_contract': ['sold', 'active', 'withdrawn'],
      'sold': [], // Final state
      'expired': ['active', 'withdrawn'],
      'withdrawn': ['active']
    };

    if (fromStatus && validTransitions[fromStatus] && !validTransitions[fromStatus].includes(toStatus)) {
      errors.push({
        field: 'status',
        message: `Cannot transition from ${fromStatus} to ${toStatus}`,
        code: 'INVALID_TRANSITION'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate business rules for listing publication
   */
  static validateListingForPublication(data: any): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields for publication
    const requiredFields = [
      'businessName',
      'industry',
      'askingPrice',
      'generalLocation',
      'description'
    ];

    for (const field of requiredFields) {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim().length === 0)) {
        errors.push({
          field,
          message: `${field} is required for publication`,
          code: 'REQUIRED_FOR_PUBLICATION'
        });
      }
    }

    // Business logic validations
    if (data.askingPrice && data.askingPrice < 1000) {
      errors.push({
        field: 'askingPrice',
        message: 'Asking price should be at least $1,000 for publication',
        code: 'BUSINESS_RULE'
      });
    }

    if (data.description && data.description.length < 50) {
      errors.push({
        field: 'description',
        message: 'Description should be at least 50 characters for publication',
        code: 'BUSINESS_RULE'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
