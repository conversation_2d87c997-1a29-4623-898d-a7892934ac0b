import { eq, and, sql, desc, asc, like, or, count, gte, lte } from "drizzle-orm";
import db from "@/db";
import { listings, listingDetails, userProfiles } from "@/db/schema";

export interface ListingFilters {
  page?: number;
  limit?: number;
  status?: string;
  industry?: string;
  assignedTo?: string;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface PaginatedListings {
  data: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * ListingQueryService - Complex queries and filtering logic
 * 
 * This service handles:
 * - Complex database queries with filtering
 * - Search functionality
 * - Pagination logic
 * - Sorting and ordering
 * - Analytics and statistics
 */
export class ListingQueryService {
  /**
   * Get listings with filtering, sorting and pagination
   */
  static async getListings(filters: ListingFilters, organizationId: string): Promise<PaginatedListings> {
    const {
      page = 1,
      limit = 20,
      status,
      industry,
      assignedTo,
      minPrice,
      maxPrice,
      location,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search
    } = filters;

    // Build where conditions
    const conditions = [eq(listings.organizationId, organizationId)];

    if (status) {
      conditions.push(eq(listings.status, status));
    }

    if (industry) {
      conditions.push(eq(listings.industry, industry));
    }

    if (assignedTo) {
      conditions.push(eq(listings.assignedTo, assignedTo));
    }

    if (minPrice) {
      conditions.push(gte(listings.askingPrice, minPrice.toString()));
    }

    if (maxPrice) {
      conditions.push(lte(listings.askingPrice, maxPrice.toString()));
    }

    if (location) {
      conditions.push(
        or(
          like(listings.generalLocation, `%${location}%`),
          like(listings.city, `%${location}%`),
          like(listings.state, `%${location}%`)
        )
      );
    }

    if (search) {
      conditions.push(
        or(
          like(listings.businessName, `%${search}%`),
          like(listings.description, `%${search}%`),
          like(listings.industry, `%${search}%`)
        )
      );
    }

    // Build sort order
    const sortColumn = this.getSortColumn(sortBy);
    const orderBy = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Calculate offset
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const [{ total }] = await db
      .select({ total: count() })
      .from(listings)
      .where(and(...conditions));

    // Get listings with pagination
    const listingsData = await db
      .select({
        id: listings.id,
        organizationId: listings.organizationId,
        createdBy: listings.createdBy,
        assignedTo: listings.assignedTo,
        businessName: listings.businessName,
        industry: listings.industry,
        askingPrice: listings.askingPrice,
        cashFlowSde: listings.cashFlowSde,
        annualRevenue: listings.annualRevenue,
        status: listings.status,
        generalLocation: listings.generalLocation,
        yearEstablished: listings.yearEstablished,
        employees: listings.employees,
        ownerHoursWeek: listings.ownerHoursWeek,
        dateListed: listings.dateListed,
        daysListed: listings.daysListed,
        title: listings.title,
        description: listings.description,
        listingType: listings.listingType,
        teamVisibility: listings.teamVisibility,
        photos: listings.photos,
        featuredPhoto: listings.featuredPhoto,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Join with user profiles to get assigned user name
        assignedToName: userProfiles.displayName,
      })
      .from(listings)
      .leftJoin(userProfiles, eq(listings.assignedTo, userProfiles.userId))
      .where(and(...conditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      data: listingsData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };
  }

  /**
   * Search listings by text query
   */
  static async searchListings(query: string, organizationId: string, limit = 10) {
    const searchConditions = [
      eq(listings.organizationId, organizationId),
      or(
        like(listings.businessName, `%${query}%`),
        like(listings.description, `%${query}%`),
        like(listings.industry, `%${query}%`),
        like(listings.generalLocation, `%${query}%`)
      )
    ];

    return await db
      .select({
        id: listings.id,
        businessName: listings.businessName,
        industry: listings.industry,
        status: listings.status,
        askingPrice: listings.askingPrice,
        generalLocation: listings.generalLocation,
        featuredPhoto: listings.featuredPhoto,
      })
      .from(listings)
      .where(and(...searchConditions))
      .orderBy(desc(listings.createdAt))
      .limit(limit);
  }

  /**
   * Get listings analytics/statistics
   */
  static async getListingStats(organizationId: string) {
    const [stats] = await db
      .select({
        total: count(),
        avgAskingPrice: sql<number>`avg(${listings.askingPrice}::numeric)`,
        avgCashFlow: sql<number>`avg(${listings.cashFlowSde}::numeric)`,
        avgRevenue: sql<number>`avg(${listings.annualRevenue}::numeric)`,
        avgDaysListed: sql<number>`avg(${listings.daysListed})`,
      })
      .from(listings)
      .where(eq(listings.organizationId, organizationId));

    return stats;
  }

  /**
   * Get listings by status
   */
  static async getListingsByStatus(organizationId: string) {
    return await db
      .select({
        status: listings.status,
        count: count(),
      })
      .from(listings)
      .where(eq(listings.organizationId, organizationId))
      .groupBy(listings.status);
  }

  /**
   * Get listings by industry
   */
  static async getListingsByIndustry(organizationId: string) {
    return await db
      .select({
        industry: listings.industry,
        count: count(),
        avgPrice: sql<number>`avg(${listings.askingPrice}::numeric)`,
      })
      .from(listings)
      .where(eq(listings.organizationId, organizationId))
      .groupBy(listings.industry)
      .orderBy(desc(count()));
  }

  /**
   * Get recently created listings
   */
  static async getRecentListings(organizationId: string, limit = 5) {
    return await db
      .select({
        id: listings.id,
        businessName: listings.businessName,
        industry: listings.industry,
        status: listings.status,
        askingPrice: listings.askingPrice,
        createdAt: listings.createdAt,
      })
      .from(listings)
      .where(eq(listings.organizationId, organizationId))
      .orderBy(desc(listings.createdAt))
      .limit(limit);
  }

  /**
   * Get listings assigned to a specific user
   */
  static async getListingsByAssignee(assignedTo: string, organizationId: string) {
    return await db
      .select()
      .from(listings)
      .where(and(
        eq(listings.organizationId, organizationId),
        eq(listings.assignedTo, assignedTo)
      ))
      .orderBy(desc(listings.updatedAt));
  }

  /**
   * Get sort column based on sortBy parameter
   */
  private static getSortColumn(sortBy: string) {
    switch (sortBy) {
      case 'business_name':
        return listings.businessName;
      case 'asking_price':
        return listings.askingPrice;
      case 'date_listed':
        return listings.dateListed;
      case 'days_listed':
        return listings.daysListed;
      case 'updated_at':
        return listings.updatedAt;
      case 'created_at':
      default:
        return listings.createdAt;
    }
  }
}
