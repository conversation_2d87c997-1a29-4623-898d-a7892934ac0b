import <PERSON> from 'papa<PERSON><PERSON>';
import { HTTPException } from 'hono/http-exception';

export interface CsvRow {
  business_name: string;
  industry: string;
  asking_price?: string;
  cash_flow_sde?: string;
  annual_revenue?: string;
  status?: string;
  general_location?: string;
  year_established?: string;
  employees?: string;
  owner_hours_week?: string;
  date_listed?: string;
  days_listed?: string;
  business_description?: string;
  brief_description?: string;
  financial_details?: string;
  operations?: string;
  growth_opportunities?: string;
  reason_for_sale?: string;
  training_period?: string;
  support_type?: string;
  financing_available?: string;
  equipment_highlights?: string;
  supplier_relationships?: string;
  real_estate_status?: string;
  lease_details?: string;
  listing_agent?: string;
  commission_rate?: string;
}

export interface ParsedCsvData {
  data: CsvRow[];
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

export class CsvParser {
  private static readonly REQUIRED_FIELDS = ['business_name', 'industry'];
  private static readonly MAX_ROWS = 1000;

  // Valid status values that match the database constraint
  private static readonly VALID_STATUS_VALUES = [
    'draft',
    'active', 
    'pending',
    'sold',
    'withdrawn'
  ] as const;

  /**
   * Validate status value against allowed values
   */
  private static validateStatus(status?: string): string {
    if (!status) return 'draft'; // default status
    
    const normalizedStatus = status.toLowerCase().trim();
    
    // Handle common variations to match actual DB constraint
    const statusMap: Record<string, string> = {
      'under contract': 'pending',
      'undercontract': 'pending',
      'under_contract': 'pending',
      'pending': 'pending',
      'closed': 'sold',
      'available': 'active',
      'listed': 'active',
      'off market': 'withdrawn',
      'off-market': 'withdrawn',
      'offmarket': 'withdrawn',
      'confidential': 'active', // Map confidential to active since DB doesn't have it
      'expired': 'withdrawn',  // Map expired to withdrawn
    };
    
    const mappedStatus = statusMap[normalizedStatus] || normalizedStatus;
    
    if (this.VALID_STATUS_VALUES.includes(mappedStatus as any)) {
      return mappedStatus;
    }
    
    // If invalid status, default to draft
    console.warn(`Invalid status value "${status}" provided, defaulting to "draft"`);
    return 'draft';
  }

  /**
   * Map CSV headers to internal field names
   */
  private static mapHeaders(header: string): string {
    const headerMap: Record<string, string> = {
      // Core business fields from sample CSV
      'business_name': 'business_name',
      'industry': 'industry',
      'asking_price': 'asking_price',
      'cash_flow_sde': 'cash_flow_sde',
      'cash_flow/sde': 'cash_flow_sde', // Handle the slash variant
      'status': 'status',
      'days_listed': 'days_listed',
      'general_location': 'general_location',
      'year_established': 'year_established',
      'annual_revenue': 'annual_revenue',
      'number_of_employees': 'employees',
      'employees': 'employees',
      'brief_description': 'brief_description',
      'reason_for_sale': 'reason_for_sale',
      'owner_hours_per_week': 'owner_hours_week',
      'owner_hours_week': 'owner_hours_week',
      'key_assets_included': 'equipment_highlights',
      'equipment_highlights': 'equipment_highlights',
      'lease_details': 'lease_details',
      'growth_opportunities': 'growth_opportunities',
      'date_listed': 'date_listed',
      'listing_agent': 'listing_agent',
      'commission_rate': 'commission_rate',
      
      // Additional detailed fields
      'business_description': 'business_description',
      'financial_details': 'financial_details',
      'operations': 'operations',
      'training_period': 'training_period',
      'support_type': 'support_type',
      'financing_available': 'financing_available',
      'supplier_relationships': 'supplier_relationships',
      'real_estate_status': 'real_estate_status',
    };

    const normalizedHeader = header.trim().toLowerCase().replace(/\s+/g, '_').replace(/\//g, '_');
    return headerMap[normalizedHeader] || normalizedHeader;
  }

  /**
   * Ultra-fast CSV parsing - minimal overhead version
   */
  static async parseCsvFileStreamlined(file: File): Promise<ParsedCsvData> {
    // Validate file type quickly
    if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
      throw new HTTPException(400, { message: 'File must be a CSV file' });
    }

    // Read file content
    const fileContent = await file.text();
    
    if (!fileContent.trim()) {
      throw new HTTPException(400, { message: 'CSV file is empty' });
    }

    // Simple CSV parsing - much faster than Papa Parse for small files
    const lines = fileContent.split('\n').filter(line => line.trim());
    if (lines.length === 0) {
      throw new HTTPException(400, { message: 'CSV file contains no data rows' });
    }

    if (lines.length > this.MAX_ROWS + 1) { // +1 for header
      throw new HTTPException(400, { 
        message: `CSV file contains too many rows. Maximum allowed: ${this.MAX_ROWS}` 
      });
    }

    // Parse header
    const delimiter = fileContent.includes(';') ? ';' : ',';
    const headers = lines[0].split(delimiter).map(h => this.mapHeaders(h.trim()));
    
    // Parse data rows
    const data: CsvRow[] = [];
    const errors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(delimiter);
      const row: any = {};
      
      headers.forEach((header, index) => {
        row[header] = values[index]?.trim() || '';
      });

      // Quick validation - only check required fields
      if (!row.business_name || !row.industry) {
        errors.push({
          row: i + 1,
          field: !row.business_name ? 'business_name' : 'industry',
          message: `Required field '${!row.business_name ? 'business_name' : 'industry'}' is missing or empty`
        });
      }

      data.push(row as CsvRow);
    }

    return { data, errors };
  }

  /**
   * Ultra-fast CSV row conversion - minimal processing version
   */
  static csvRowToListingDataStreamlined(row: CsvRow, organizationId: string, createdBy: string): any {
    // Helper function to safely parse numbers - simplified
    const safeParseNumber = (value?: string): number | undefined => {
      if (!value || value.trim() === '') return undefined;
      const parsed = parseFloat(value.replace(/[,$]/g, ''));
      return isNaN(parsed) ? undefined : parsed;
    };

    // Helper function to safely parse integers - simplified  
    const safeParseInt = (value?: string): number | undefined => {
      if (!value || value.trim() === '') return undefined;
      const parsed = parseInt(value.replace(/[,$]/g, ''), 10);
      return isNaN(parsed) ? undefined : parsed;
    };

    return {
      // Required fields - using camelCase to match CreateListingData type
      businessName: row.business_name,
      industry: row.industry,

      // Core business fields - using camelCase to match CreateListingData type
      askingPrice: safeParseNumber(row.asking_price),
      cashFlowSde: safeParseNumber(row.cash_flow_sde),
      annualRevenue: safeParseNumber(row.annual_revenue),
      status: this.validateStatus(row.status),
      generalLocation: row.general_location,
      yearEstablished: safeParseInt(row.year_established),
      employees: safeParseInt(row.employees),
      ownerHoursWeek: safeParseInt(row.owner_hours_week),
      dateListed: row.date_listed,

      // Legacy fields for compatibility
      title: row.business_name,
      description: row.brief_description || row.business_description,

      // Metadata - using camelCase to match CreateListingData type
      organizationId: organizationId,
      createdBy: createdBy,

      // Minimal details object - skip complex processing
      details: row.brief_description ? {
        brief_description: row.brief_description,
        reason_for_sale: row.reason_for_sale,
        growth_opportunities: row.growth_opportunities ? [row.growth_opportunities] : [],
        financing_available: false,
        equipment_highlights: row.equipment_highlights ? [row.equipment_highlights] : [],
        supplier_relationships: row.supplier_relationships,
        financial_details: {},
        operations: {},
        lease_details: {}
      } : undefined
    };
  }

  /**
   * Parse CSV file content and validate required fields - Optimized version
   */
  static async parseCsvFile(file: File): Promise<ParsedCsvData> {
    return this.parseCsvFileStreamlined(file);
  }

  /**
   * Convert CSV row to listing data format - Optimized version
   */
  static csvRowToListingData(row: CsvRow, organizationId: string, createdBy: string): any {
    return this.csvRowToListingDataStreamlined(row, organizationId, createdBy);
  }
}
